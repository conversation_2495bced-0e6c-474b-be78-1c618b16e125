/**
 * نظام جمع بصمة الجهاز والمتصفح
 * Device and Browser Fingerprinting System
 */

class DeviceFingerprinting {
    constructor() {
        this.fingerprint = {};
        this.sessionId = this.generateSessionId();
    }

    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // جمع معلومات المتصفح الأساسية
    async collectBasicInfo() {
        this.fingerprint.basic = {
            userAgent: navigator.userAgent,
            language: navigator.language,
            languages: navigator.languages,
            platform: navigator.platform,
            cookieEnabled: navigator.cookieEnabled,
            doNotTrack: navigator.doNotTrack,
            onLine: navigator.onLine,
            javaEnabled: navigator.javaEnabled ? navigator.javaEnabled() : false
        };
    }

    // جمع معلومات الشاشة والعرض
    collectScreenInfo() {
        this.fingerprint.screen = {
            width: screen.width,
            height: screen.height,
            availWidth: screen.availWidth,
            availHeight: screen.availHeight,
            colorDepth: screen.colorDepth,
            pixelDepth: screen.pixelDepth,
            devicePixelRatio: window.devicePixelRatio || 1,
            orientation: screen.orientation ? screen.orientation.type : 'unknown'
        };

        // معلومات نافذة المتصفح
        this.fingerprint.window = {
            innerWidth: window.innerWidth,
            innerHeight: window.innerHeight,
            outerWidth: window.outerWidth,
            outerHeight: window.outerHeight,
            scrollX: window.scrollX,
            scrollY: window.scrollY
        };
    }

    // جمع معلومات المنطقة الزمنية
    collectTimezoneInfo() {
        const now = new Date();
        this.fingerprint.timezone = {
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            timezoneOffset: now.getTimezoneOffset(),
            localTime: now.toLocaleString(),
            utcTime: now.toUTCString()
        };
    }

    // جمع معلومات الخطوط المتاحة
    async collectFonts() {
        const fonts = [
            'Arial', 'Helvetica', 'Times New Roman', 'Courier New', 'Verdana',
            'Georgia', 'Palatino', 'Garamond', 'Bookman', 'Comic Sans MS',
            'Trebuchet MS', 'Arial Black', 'Impact', 'Tahoma', 'Geneva',
            'Lucida Console', 'Monaco', 'Courier', 'serif', 'sans-serif'
        ];

        this.fingerprint.fonts = [];
        
        for (const font of fonts) {
            if (await this.isFontAvailable(font)) {
                this.fingerprint.fonts.push(font);
            }
        }
    }

    isFontAvailable(fontName) {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            
            context.font = '12px monospace';
            const baselineText = 'mmmmmmmmmmlli';
            const baselineWidth = context.measureText(baselineText).width;
            
            context.font = `12px ${fontName}, monospace`;
            const testWidth = context.measureText(baselineText).width;
            
            resolve(baselineWidth !== testWidth);
        });
    }

    // جمع معلومات الإضافات
    collectPlugins() {
        this.fingerprint.plugins = [];
        
        if (navigator.plugins) {
            for (let i = 0; i < navigator.plugins.length; i++) {
                const plugin = navigator.plugins[i];
                this.fingerprint.plugins.push({
                    name: plugin.name,
                    description: plugin.description,
                    filename: plugin.filename,
                    version: plugin.version
                });
            }
        }
    }

    // جمع معلومات Canvas fingerprint
    collectCanvasFingerprint() {
        try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = 200;
            canvas.height = 50;
            
            ctx.textBaseline = 'top';
            ctx.font = '14px Arial';
            ctx.fillStyle = '#f60';
            ctx.fillRect(125, 1, 62, 20);
            ctx.fillStyle = '#069';
            ctx.fillText('Canvas fingerprint test 🔒', 2, 15);
            ctx.fillStyle = 'rgba(102, 204, 0, 0.7)';
            ctx.fillText('Canvas fingerprint test 🔒', 4, 17);
            
            this.fingerprint.canvas = canvas.toDataURL();
        } catch (e) {
            this.fingerprint.canvas = 'error';
        }
    }

    // جمع معلومات WebGL
    collectWebGLInfo() {
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            
            if (gl) {
                this.fingerprint.webgl = {
                    vendor: gl.getParameter(gl.VENDOR),
                    renderer: gl.getParameter(gl.RENDERER),
                    version: gl.getParameter(gl.VERSION),
                    shadingLanguageVersion: gl.getParameter(gl.SHADING_LANGUAGE_VERSION),
                    extensions: gl.getSupportedExtensions()
                };
            } else {
                this.fingerprint.webgl = 'not_supported';
            }
        } catch (e) {
            this.fingerprint.webgl = 'error';
        }
    }

    // جمع معلومات الصوت
    async collectAudioFingerprint() {
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const analyser = audioContext.createAnalyser();
            const gainNode = audioContext.createGain();
            const scriptProcessor = audioContext.createScriptProcessor(4096, 1, 1);
            
            oscillator.type = 'triangle';
            oscillator.frequency.setValueAtTime(10000, audioContext.currentTime);
            
            gainNode.gain.setValueAtTime(0, audioContext.currentTime);
            
            oscillator.connect(analyser);
            analyser.connect(scriptProcessor);
            scriptProcessor.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.start(0);
            
            const audioData = new Float32Array(analyser.frequencyBinCount);
            analyser.getFloatFrequencyData(audioData);
            
            oscillator.stop();
            audioContext.close();
            
            this.fingerprint.audio = Array.from(audioData).slice(0, 30).join(',');
        } catch (e) {
            this.fingerprint.audio = 'error';
        }
    }

    // جمع معلومات التخزين المحلي
    collectStorageInfo() {
        this.fingerprint.storage = {
            localStorage: typeof(Storage) !== 'undefined' && localStorage !== null,
            sessionStorage: typeof(Storage) !== 'undefined' && sessionStorage !== null,
            indexedDB: 'indexedDB' in window,
            webSQL: 'openDatabase' in window,
            cookies: navigator.cookieEnabled
        };

        // محاولة قياس حجم التخزين المتاح
        if (this.fingerprint.storage.localStorage) {
            try {
                const testKey = 'storage_test';
                const testValue = 'x'.repeat(1024); // 1KB
                let size = 0;
                
                while (size < 10240) { // حتى 10MB
                    try {
                        localStorage.setItem(testKey + size, testValue);
                        size += 1024;
                    } catch (e) {
                        break;
                    }
                }
                
                // تنظيف البيانات التجريبية
                for (let i = 0; i < size; i += 1024) {
                    localStorage.removeItem(testKey + i);
                }
                
                this.fingerprint.storage.localStorageSize = size;
            } catch (e) {
                this.fingerprint.storage.localStorageSize = 'error';
            }
        }
    }

    // جمع معلومات الشبكة
    async collectNetworkInfo() {
        this.fingerprint.network = {
            connection: navigator.connection || navigator.mozConnection || navigator.webkitConnection,
            onLine: navigator.onLine
        };

        if (this.fingerprint.network.connection) {
            this.fingerprint.network.connectionInfo = {
                effectiveType: this.fingerprint.network.connection.effectiveType,
                downlink: this.fingerprint.network.connection.downlink,
                rtt: this.fingerprint.network.connection.rtt,
                saveData: this.fingerprint.network.connection.saveData
            };
        }

        // محاولة الحصول على IP العام (للأغراض التعليمية فقط)
        try {
            // استخدام خدمة محلية بدلاً من خدمة خارجية
            this.fingerprint.network.publicIP = 'educational_mode';
        } catch (e) {
            this.fingerprint.network.publicIP = 'unknown';
        }
    }

    // جمع معلومات الموقع الجغرافي (مع طلب الإذن)
    async collectLocationInfo() {
        return new Promise((resolve) => {
            if ('geolocation' in navigator) {
                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        this.fingerprint.location = {
                            latitude: position.coords.latitude,
                            longitude: position.coords.longitude,
                            accuracy: position.coords.accuracy,
                            altitude: position.coords.altitude,
                            heading: position.coords.heading,
                            speed: position.coords.speed,
                            timestamp: position.timestamp
                        };
                        resolve();
                    },
                    (error) => {
                        this.fingerprint.location = {
                            error: error.message,
                            code: error.code
                        };
                        resolve();
                    },
                    {
                        enableHighAccuracy: true,
                        timeout: 10000,
                        maximumAge: 60000
                    }
                );
            } else {
                this.fingerprint.location = 'not_supported';
                resolve();
            }
        });
    }

    // جمع جميع المعلومات
    async collectAllFingerprints() {
        await this.collectBasicInfo();
        this.collectScreenInfo();
        this.collectTimezoneInfo();
        await this.collectFonts();
        this.collectPlugins();
        this.collectCanvasFingerprint();
        this.collectWebGLInfo();
        await this.collectAudioFingerprint();
        this.collectStorageInfo();
        await this.collectNetworkInfo();

        // إضافة معلومات إضافية
        this.fingerprint.timestamp = new Date().toISOString();
        this.fingerprint.sessionId = this.sessionId;
        this.fingerprint.url = window.location.href;
        this.fingerprint.referrer = document.referrer;

        return this.fingerprint;
    }

    // إرسال البيانات إلى الخادم
    async sendFingerprint() {
        try {
            const response = await fetch('../api/collect-fingerprint.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(this.fingerprint)
            });

            if (response.ok) {
                console.log('Fingerprint sent successfully');
            }
        } catch (error) {
            console.error('Error sending fingerprint:', error);
        }
    }
}

// إنشاء مثيل عام للاستخدام
window.deviceFingerprinting = new DeviceFingerprinting();
