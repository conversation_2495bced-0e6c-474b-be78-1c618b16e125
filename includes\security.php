<?php
/**
 * ملف الأمان والحماية
 * Security and Protection Functions
 */

// منع الوصول المباشر
if (!defined('DB_HOST')) {
    die('Direct access not allowed');
}

/**
 * تنظيف البيانات المدخلة
 */
function sanitizeInput($input) {
    if (is_array($input)) {
        return array_map('sanitizeInput', $input);
    }
    
    $input = trim($input);
    $input = stripslashes($input);
    $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
    
    return $input;
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * التحقق من صحة IP
 */
function validateIP($ip) {
    return filter_var($ip, FILTER_VALIDATE_IP) !== false;
}

/**
 * إنشاء رمز CSRF
 */
function generateCSRFToken() {
    if (!isset($_SESSION)) {
        session_start();
    }
    
    $token = bin2hex(random_bytes(32));
    $_SESSION['csrf_token'] = $token;
    
    return $token;
}

/**
 * التحقق من رمز CSRF
 */
function validateCSRFToken($token) {
    if (!isset($_SESSION)) {
        session_start();
    }
    
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * تحديد معدل الطلبات
 */
function rateLimitCheck($identifier, $maxRequests = 100, $timeWindow = 3600) {
    $cacheFile = LOG_PATH . 'rate_limit_' . md5($identifier) . '.json';
    
    $now = time();
    $requests = [];
    
    // قراءة الطلبات السابقة
    if (file_exists($cacheFile)) {
        $data = json_decode(file_get_contents($cacheFile), true);
        if ($data && isset($data['requests'])) {
            $requests = $data['requests'];
        }
    }
    
    // تنظيف الطلبات القديمة
    $requests = array_filter($requests, function($timestamp) use ($now, $timeWindow) {
        return ($now - $timestamp) < $timeWindow;
    });
    
    // فحص الحد الأقصى
    if (count($requests) >= $maxRequests) {
        return false;
    }
    
    // إضافة الطلب الحالي
    $requests[] = $now;
    
    // حفظ البيانات
    file_put_contents($cacheFile, json_encode(['requests' => $requests]));
    
    return true;
}

/**
 * كشف محاولات الهجوم
 */
function detectAttackPatterns($input) {
    $patterns = [
        'sql_injection' => '/(\bUNION\b|\bSELECT\b|\bINSERT\b|\bDELETE\b|\bDROP\b)/i',
        'xss' => '/(<script|javascript:|onload=|onerror=)/i',
        'path_traversal' => '/(\.\.\/|\.\.\\\\)/i',
        'command_injection' => '/(\||\;|\&|\$\(|\`)/i'
    ];
    
    $detected = [];
    
    foreach ($patterns as $type => $pattern) {
        if (preg_match($pattern, $input)) {
            $detected[] = $type;
        }
    }
    
    return $detected;
}

/**
 * تسجيل محاولة هجوم
 */
function logSecurityIncident($type, $details, $severity = 'MEDIUM') {
    $incident = [
        'timestamp' => date('Y-m-d H:i:s'),
        'type' => $type,
        'severity' => $severity,
        'details' => $details,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown'
    ];
    
    $logFile = LOG_PATH . 'security_incidents_' . date('Y-m-d') . '.log';
    $logEntry = json_encode($incident) . PHP_EOL;
    
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    
    // إرسال تنبيه في حالة الخطورة العالية
    if ($severity === 'HIGH' || $severity === 'CRITICAL') {
        sendSecurityAlert($incident);
    }
}

/**
 * إرسال تنبيه أمني
 */
function sendSecurityAlert($incident) {
    // يمكن إضافة إرسال بريد إلكتروني أو إشعار هنا
    error_log("SECURITY ALERT: " . json_encode($incident));
}

/**
 * التحقق من قوة كلمة المرور
 */
function validatePasswordStrength($password) {
    $score = 0;
    $feedback = [];
    
    // الطول
    if (strlen($password) >= 8) {
        $score += 2;
    } else {
        $feedback[] = 'Password should be at least 8 characters';
    }
    
    // الأحرف الكبيرة
    if (preg_match('/[A-Z]/', $password)) {
        $score += 1;
    } else {
        $feedback[] = 'Password should contain uppercase letters';
    }
    
    // الأحرف الصغيرة
    if (preg_match('/[a-z]/', $password)) {
        $score += 1;
    } else {
        $feedback[] = 'Password should contain lowercase letters';
    }
    
    // الأرقام
    if (preg_match('/[0-9]/', $password)) {
        $score += 1;
    } else {
        $feedback[] = 'Password should contain numbers';
    }
    
    // الرموز الخاصة
    if (preg_match('/[^A-Za-z0-9]/', $password)) {
        $score += 1;
    } else {
        $feedback[] = 'Password should contain special characters';
    }
    
    return [
        'score' => $score,
        'strength' => getPasswordStrengthLevel($score),
        'feedback' => $feedback
    ];
}

/**
 * تحديد مستوى قوة كلمة المرور
 */
function getPasswordStrengthLevel($score) {
    if ($score >= 5) return 'very_strong';
    if ($score >= 4) return 'strong';
    if ($score >= 3) return 'medium';
    if ($score >= 2) return 'weak';
    return 'very_weak';
}

/**
 * تشفير كلمة المرور
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_ARGON2ID, [
        'memory_cost' => 65536,
        'time_cost' => 4,
        'threads' => 3
    ]);
}

/**
 * التحقق من كلمة المرور
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * إنشاء رمز عشوائي آمن
 */
function generateSecureToken($length = 32) {
    return bin2hex(random_bytes($length));
}

/**
 * تنظيف اسم الملف
 */
function sanitizeFileName($filename) {
    // إزالة الأحرف الخطيرة
    $filename = preg_replace('/[^a-zA-Z0-9._-]/', '', $filename);
    
    // منع الأسماء الخطيرة
    $dangerousNames = ['con', 'prn', 'aux', 'nul', 'com1', 'com2', 'lpt1', 'lpt2'];
    $name = strtolower(pathinfo($filename, PATHINFO_FILENAME));
    
    if (in_array($name, $dangerousNames)) {
        $filename = 'safe_' . $filename;
    }
    
    return $filename;
}

/**
 * التحقق من نوع الملف
 */
function validateFileType($filename, $allowedTypes = ['jpg', 'jpeg', 'png', 'gif']) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    return in_array($extension, $allowedTypes);
}

/**
 * إنشاء جلسة آمنة
 */
function startSecureSession() {
    // إعدادات الجلسة الآمنة
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', 1);
    ini_set('session.use_strict_mode', 1);
    ini_set('session.cookie_samesite', 'Strict');
    
    if (!isset($_SESSION)) {
        session_start();
    }
    
    // تجديد معرف الجلسة
    if (!isset($_SESSION['initiated'])) {
        session_regenerate_id(true);
        $_SESSION['initiated'] = true;
    }
}

/**
 * تنظيف الجلسة
 */
function destroySecureSession() {
    if (isset($_SESSION)) {
        $_SESSION = [];
        
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
        
        session_destroy();
    }
}

/**
 * فحص الصلاحيات
 */
function checkPermission($requiredPermission, $userPermissions = []) {
    return in_array($requiredPermission, $userPermissions) || in_array('admin', $userPermissions);
}

/**
 * تسجيل محاولة دخول
 */
function logLoginAttempt($username, $success, $ip) {
    $attempt = [
        'timestamp' => date('Y-m-d H:i:s'),
        'username' => $username,
        'success' => $success,
        'ip' => $ip,
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ];
    
    $logFile = LOG_PATH . 'login_attempts_' . date('Y-m-d') . '.log';
    $logEntry = json_encode($attempt) . PHP_EOL;
    
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

/**
 * فحص محاولات الدخول المتكررة
 */
function checkBruteForce($username, $ip, $maxAttempts = 5, $timeWindow = 900) {
    $logFile = LOG_PATH . 'login_attempts_' . date('Y-m-d') . '.log';
    
    if (!file_exists($logFile)) {
        return false;
    }
    
    $attempts = file($logFile, FILE_IGNORE_NEW_LINES);
    $failedAttempts = 0;
    $now = time();
    
    foreach ($attempts as $attempt) {
        $data = json_decode($attempt, true);
        if (!$data) continue;
        
        $attemptTime = strtotime($data['timestamp']);
        
        // فحص النافذة الزمنية
        if (($now - $attemptTime) > $timeWindow) {
            continue;
        }
        
        // فحص IP أو اسم المستخدم
        if (($data['ip'] === $ip || $data['username'] === $username) && !$data['success']) {
            $failedAttempts++;
        }
    }
    
    return $failedAttempts >= $maxAttempts;
}
?>
