<?php
/**
 * سكريبت إعداد قاعدة البيانات
 * Database Setup Script
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'phishing_education';

echo "<h1>إعداد قاعدة البيانات</h1>";
echo "<p>جاري إنشاء قاعدة البيانات والجداول...</p>";

try {
    // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // إنشاء قاعدة البيانات
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p style='color: green;'>✅ تم إنشاء قاعدة البيانات: $database</p>";
    
    // الاتصال بقاعدة البيانات المحددة
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // قراءة ملف SQL
    $sqlFile = 'database/setup.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("ملف SQL غير موجود: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    
    // تنظيف SQL وإزالة التعليقات
    $lines = explode("\n", $sql);
    $cleanedLines = [];

    foreach ($lines as $line) {
        $line = trim($line);
        // تجاهل الأسطر الفارغة والتعليقات
        if (empty($line) || strpos($line, '--') === 0) {
            continue;
        }
        $cleanedLines[] = $line;
    }

    $cleanedSql = implode("\n", $cleanedLines);

    // تقسيم الاستعلامات بناءً على ؛ في نهاية السطر
    $statements = array_filter(array_map('trim', preg_split('/;\s*$/m', $cleanedSql)));

    $successCount = 0;
    foreach ($statements as $statement) {
        if (empty($statement)) {
            continue;
        }

        try {
            $pdo->exec($statement);
            $successCount++;

            // استخراج اسم الجدول من CREATE TABLE
            if (preg_match('/CREATE TABLE.*?`?(\w+)`?/i', $statement, $matches)) {
                echo "<p style='color: green;'>✅ تم إنشاء الجدول: {$matches[1]}</p>";
            }
        } catch (PDOException $e) {
            // تجاهل أخطاء "already exists"
            if (strpos($e->getMessage(), 'already exists') === false) {
                echo "<p style='color: red;'>❌ خطأ في الاستعلام: " . $e->getMessage() . "</p>";
                echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>" . htmlspecialchars(substr($statement, 0, 200)) . "...</pre>";
            } else {
                echo "<p style='color: blue;'>ℹ️ الجدول موجود مسبقاً</p>";
            }
        }
    }
    
    echo "<p style='color: green;'>✅ تم تنفيذ $successCount استعلام بنجاح</p>";
    
    // التحقق من الجداول
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h3>الجداول المنشأة:</h3>";
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li style='color: green;'>✅ $table</li>";
    }
    echo "</ul>";
    
    // إنشاء مستخدم إداري افتراضي
    $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT IGNORE INTO admin_users (username, password_hash, email) VALUES (?, ?, ?)");
    $stmt->execute(['admin', $adminPassword, '<EMAIL>']);
    
    echo "<p style='color: blue;'>👤 تم إنشاء مستخدم إداري:</p>";
    echo "<ul>";
    echo "<li><strong>اسم المستخدم:</strong> admin</li>";
    echo "<li><strong>كلمة المرور:</strong> admin123</li>";
    echo "</ul>";
    
    echo "<h2 style='color: green;'>🎉 تم إعداد قاعدة البيانات بنجاح!</h2>";
    echo "<p><a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>العودة للصفحة الرئيسية</a></p>";
    echo "<p><a href='test.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>اختبار النظام</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
    echo "<h3>خطوات الحل:</h3>";
    echo "<ol>";
    echo "<li>تأكد من تشغيل خادم MySQL في XAMPP</li>";
    echo "<li>تحقق من إعدادات الاتصال (المضيف، اسم المستخدم، كلمة المرور)</li>";
    echo "<li>تأكد من وجود ملف database/setup.sql</li>";
    echo "</ol>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: #f5f5f5;
}

h1, h2, h3 {
    color: #333;
}

p, li {
    line-height: 1.6;
}

ul, ol {
    margin: 10px 0;
}
</style>
