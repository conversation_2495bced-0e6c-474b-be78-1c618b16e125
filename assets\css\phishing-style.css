/* تنسيقات صفحة التصيد التعليمية */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    direction: rtl;
    text-align: right;
}

.warning-banner {
    background: #ff4444;
    color: white;
    padding: 10px;
    text-align: center;
    font-weight: bold;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    animation: pulse 2s infinite;
}

.warning-banner button {
    background: white;
    color: #ff4444;
    border: none;
    padding: 5px 15px;
    margin-right: 10px;
    border-radius: 3px;
    cursor: pointer;
    font-weight: bold;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 60px 20px 20px;
}

.bank-header {
    background: white;
    padding: 20px;
    border-radius: 10px 10px 0 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    width: 60px;
    height: 60px;
    background: #1e3a8a;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

.bank-header h1 {
    color: #1e3a8a;
    font-size: 24px;
    flex-grow: 1;
    text-align: center;
}

.security-badge {
    background: #10b981;
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: bold;
}

.login-section {
    background: white;
    padding: 40px;
    border-radius: 0 0 10px 10px;
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
}

.login-card h2 {
    color: #1e3a8a;
    margin-bottom: 10px;
    text-align: center;
}

.security-notice {
    color: #666;
    text-align: center;
    margin-bottom: 30px;
    font-size: 14px;
}

.login-form {
    max-width: 400px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: 20px;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #333;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.form-group input:focus {
    outline: none;
    border-color: #1e3a8a;
    box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.password-toggle {
    position: absolute;
    left: 12px;
    top: 38px;
    cursor: pointer;
    user-select: none;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
}

.login-btn, .verify-btn {
    width: 100%;
    background: #1e3a8a;
    color: white;
    padding: 15px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s;
}

.login-btn:hover, .verify-btn:hover {
    background: #1e40af;
}

.additional-info {
    text-align: center;
    margin-top: 20px;
}

.additional-info a {
    color: #1e3a8a;
    text-decoration: none;
}

.additional-info a:hover {
    text-decoration: underline;
}

.hidden {
    display: none;
}

.verification-card {
    background: #f8fafc;
    padding: 30px;
    border-radius: 10px;
    margin-top: 20px;
    border: 2px solid #e5e7eb;
}

.verification-card h3 {
    color: #1e3a8a;
    margin-bottom: 10px;
    text-align: center;
}

.form-row {
    display: flex;
    gap: 15px;
}

.form-row .form-group {
    flex: 1;
}

.bank-footer {
    background: #1e3a8a;
    color: white;
    padding: 20px;
    text-align: center;
    border-radius: 10px;
    margin-top: 20px;
}

.security-info {
    margin-top: 10px;
    display: flex;
    justify-content: center;
    gap: 20px;
    font-size: 14px;
}

/* تأثيرات بصرية لزيادة المصداقية */
.login-card {
    position: relative;
    overflow: hidden;
}

.login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #1e3a8a, #10b981, #f59e0b);
}

/* تنسيقات متجاوبة */
@media (max-width: 768px) {
    .container {
        padding: 60px 10px 10px;
    }
    
    .bank-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .login-section {
        padding: 20px;
    }
    
    .form-row {
        flex-direction: column;
    }
    
    .security-info {
        flex-direction: column;
        gap: 10px;
    }
}

/* تأثيرات التحميل */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

.spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #1e3a8a;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
    display: inline-block;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
