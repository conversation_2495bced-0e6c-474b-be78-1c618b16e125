<?php
/**
 * الصفحة الرئيسية للمشروع التعليمي
 * Main Page for Educational Project
 */
session_start();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مشروع التصيد الإلكتروني التعليمي</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .warning-box {
            background: #ff4444;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.8; }
            100% { opacity: 1; }
        }
        
        .cards-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .card p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .btn {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 8px;
            transition: background 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 16px;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .btn-danger {
            background: #ff4444;
        }
        
        .btn-danger:hover {
            background: #cc3333;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .features-list {
            list-style: none;
            padding: 0;
        }
        
        .features-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .features-list li:before {
            content: "✓ ";
            color: #28a745;
            font-weight: bold;
        }
        
        .footer {
            text-align: center;
            color: white;
            margin-top: 40px;
            padding: 20px;
            background: rgba(0,0,0,0.2);
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎓 مشروع التصيد الإلكتروني التعليمي</h1>
            <p>Educational Phishing Simulation Project</p>
        </div>
        
        <div class="warning-box">
            <h2>⚠️ تحذير مهم جداً ⚠️</h2>
            <p><strong>هذا مشروع تعليمي فقط لفهم تقنيات التصيد الإلكتروني وطرق الحماية منها</strong></p>
            <p><strong>لا يجب استخدامه لأي أغراض ضارة أو غير قانونية</strong></p>
            <p><strong>This is an educational project only to understand phishing techniques and protection methods</strong></p>
        </div>
        
        <div class="cards-container">
            <div class="card">
                <h3>🎯 صفحة التصيد التعليمية</h3>
                <p>تجربة محاكاة التصيد الإلكتروني لفهم كيفية عمل هذه الهجمات وطرق اكتشافها.</p>
                <ul class="features-list">
                    <li>جمع بصمة الجهاز والمتصفح</li>
                    <li>تسجيل ضغطات المفاتيح</li>
                    <li>التقاط صور من الكاميرا (بإذن)</li>
                    <li>جمع ملفات تعريف الارتباط</li>
                </ul>
                <a href="phishing-pages/index.html" class="btn btn-danger">دخول الصفحة التعليمية</a>
            </div>
            
            <div class="card">
                <h3>🧪 اختبار النظام</h3>
                <p>صفحة اختبار شاملة للتأكد من عمل جميع مكونات النظام بشكل صحيح.</p>
                <ul class="features-list">
                    <li>اختبار قاعدة البيانات</li>
                    <li>اختبار جمع البيانات</li>
                    <li>اختبار الكاميرا والمفاتيح</li>
                    <li>سجل الأحداث المباشر</li>
                </ul>
                <a href="test.php" class="btn btn-success">بدء الاختبار</a>
            </div>
            
            <div class="card">
                <h3>⚙️ لوحة التحكم الإدارية</h3>
                <p>واجهة إدارية لعرض البيانات المجمعة وإدارة النظام.</p>
                <ul class="features-list">
                    <li>عرض البيانات المجمعة</li>
                    <li>إحصائيات مفصلة</li>
                    <li>إدارة الجلسات</li>
                    <li>تصدير البيانات</li>
                </ul>
                <a href="admin/" class="btn">دخول لوحة التحكم</a>
            </div>
            
            <div class="card">
                <h3>📚 الدليل التعليمي</h3>
                <p>دليل شامل لفهم تقنيات التصيد الإلكتروني وطرق الحماية منها.</p>
                <ul class="features-list">
                    <li>شرح تقنيات التصيد</li>
                    <li>طرق الكشف والحماية</li>
                    <li>أمثلة عملية</li>
                    <li>نصائح الأمان</li>
                </ul>
                <a href="protection/" class="btn">قراءة الدليل</a>
            </div>
        </div>
        
        <div class="footer">
            <h3>📋 معلومات المشروع</h3>
            <p><strong>الهدف:</strong> تعليم تقنيات التصيد الإلكتروني لأغراض الحماية والوقاية</p>
            <p><strong>الاستخدام:</strong> للأغراض التعليمية فقط في بيئة محكمة</p>
            <p><strong>المسؤولية:</strong> المستخدم مسؤول عن الاستخدام الأخلاقي والقانوني</p>
            
            <div style="margin-top: 20px;">
                <strong>متطلبات التشغيل:</strong>
                <ul style="list-style: none; padding: 0; margin-top: 10px;">
                    <li>✓ XAMPP أو خادم ويب مع PHP 7.4+</li>
                    <li>✓ MySQL 5.7+ أو MariaDB</li>
                    <li>✓ متصفح حديث يدعم JavaScript</li>
                    <li>✓ إذن الكاميرا والموقع (اختياري)</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        // تحذير إضافي في وحدة التحكم
        console.log('%c🚨 تحذير أمني 🚨', 'color: red; font-size: 20px; font-weight: bold;');
        console.log('%cهذا مشروع تعليمي - لا تدخل معلومات حقيقية', 'color: orange; font-size: 16px;');
        console.log('%cThis is an educational project - Do not enter real information', 'color: orange; font-size: 16px;');
        
        // إظهار تحذير عند محاولة فتح أدوات المطور
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F12' || (e.ctrlKey && e.shiftKey && e.key === 'I')) {
                alert('تذكير: هذا مشروع تعليمي - استخدم المعلومات المكتسبة بمسؤولية');
            }
        });
        
        // تحذير عند مغادرة الصفحة
        window.addEventListener('beforeunload', function(e) {
            if (document.referrer.includes('phishing-pages')) {
                e.preventDefault();
                e.returnValue = 'هل أنت متأكد من مغادرة الجلسة التعليمية؟';
            }
        });
    </script>
</body>
</html>
