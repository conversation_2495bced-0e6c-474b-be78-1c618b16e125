# مشروع التصيد الإلكتروني التعليمي
## Educational Phishing Simulation Project

⚠️ **تحذير مهم / Important Warning** ⚠️

هذا المشروع مخصص للأغراض التعليمية فقط لفهم تقنيات التصيد الإلكتروني وطرق الحماية منها.
This project is for educational purposes only to understand phishing techniques and protection methods.

**لا يجب استخدام هذا المشروع لأي أغراض ضارة أو غير قانونية**
**This project should not be used for any malicious or illegal purposes**

## الهدف من المشروع / Project Purpose

- تعليم كيفية عمل هجمات التصيد الإلكتروني
- فهم طرق جمع المعلومات الشخصية
- تطوير الوعي الأمني لدى المستخدمين
- تعلم تقنيات الحماية والوقاية

## هيكل المشروع / Project Structure

```
phishing/
├── admin/              # لوحة التحكم الإدارية
├── assets/             # الملفات الثابتة (CSS, JS, Images)
├── config/             # ملفات التكوين
├── database/           # قاعدة البيانات وسكريبتات SQL
├── includes/           # الملفات المشتركة
├── logs/               # ملفات السجلات
├── protection/         # نظام الحماية والتحذيرات
├── phishing-pages/     # صفحات التصيد المختلفة
└── api/                # واجهات برمجة التطبيقات
```

## المتطلبات / Requirements

- PHP 7.4+
- MySQL 5.7+
- Apache/Nginx
- XAMPP (للتطوير المحلي)

## التثبيت / Installation

1. انسخ المشروع إلى مجلد htdocs في XAMPP
2. قم بتشغيل XAMPP
3. أنشئ قاعدة البيانات باستخدام ملف database/setup.sql
4. قم بتعديل ملف config/database.php
5. افتح المشروع في المتصفح

## الاستخدام المسؤول / Responsible Usage

- استخدم هذا المشروع في بيئة تعليمية محكمة فقط
- لا تستهدف أشخاص حقيقيين دون موافقتهم
- احترم القوانين المحلية والدولية
- استخدم المعرفة المكتسبة لتحسين الأمان

## إخلاء المسؤولية / Disclaimer

المطورون غير مسؤولين عن أي استخدام غير قانوني أو ضار لهذا المشروع.
The developers are not responsible for any illegal or harmful use of this project.
